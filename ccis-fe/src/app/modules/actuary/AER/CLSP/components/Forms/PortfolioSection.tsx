import React from "react";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import Tabs from "@components/common/Tabs";
import { PiMinusCircle, PiPlusCircle } from "react-icons/pi";

interface PortfolioSectionProps {
  coverageBasis: string;
  portfolio: any;
  onAddShareCapitalYear: () => void;
  onAddShareCapitalAge: () => void;
  onAddSavingsYear: () => void;
  onAddSavingsAge: () => void;
  onAddTimeDepositYear: () => void;
  onAddTimeDepositAge: () => void;
  onRemoveItem: (type: string, index: number) => void;
  onUpdatePortfolio: (path: string, rows: any[]) => void;
  isSelected: (value: string) => boolean;
  COVERAGE_BASIS: {
    SHARE_CAPITAL: string;
    SAVINGS: string;
    TIME_DEPOSIT: string;
  };
}

const PortfolioSection: React.FC<PortfolioSectionProps> = ({
  coverageBasis,
  portfolio,
  onAddShareCapitalYear,
  onAddShareCapitalAge,
  onAddSavingsYear,
  onAddSavingsAge,
  onAddTimeDepositYear,
  onAddTimeDepositAge,
  onRemoveItem,
  onUpdatePortfolio,
  isSelected,
  COVERAGE_BASIS,
}) => {
  return (
    <div className="border border-gray/10 bg-[#F6F6F680] p-6 mb-8 shadow-sm">
      <h3 className="text-lg text-primary font-[600] text-[16px] mb-3">PORTFOLIO</h3>
      <div className="grid grid-cols-12 gap-10">
        {coverageBasis.length <= 0 && (
          <div className="col-span-12 p-10 flex flex-row flex-nowrap items-center justify-center">
            <span className="text-gray/50">Select Coverage Basis to Deploy Portfolio</span>
          </div>
        )}

        {/* ===== SHARE CAPITAL ===== */}
        {isSelected(COVERAGE_BASIS.SHARE_CAPITAL) && (
          <div className="col-span-12 space-y-1">
            <span className="block text-center text-primary text-[18px] font-poppins-medium">TOTAL SHARE CAPITAL</span>
            <Tabs
              headers={["YEAR", "AGE"]}
              contents={[
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddShareCapitalYear}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "Years", key: "years", number: true },
                      { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true },
                      { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true },
                      { header: "Total Amount", key: "totalAmount", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.years.shareCapital", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.years.shareCapital}
                    onChange={(rows) => onUpdatePortfolio("portfolio.years.shareCapital", rows)}
                  />
                </>,
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddShareCapitalAge}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "AGE FROM", key: "ageFrom", number: true },
                      { header: "AGE TO", key: "ageTo", number: true },
                      { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true },
                      { header: "TOTAL SHARE CAPITAL", key: "totalShareCapital", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.ages.shareCapital", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.ages.shareCapital}
                    onChange={(rows) => onUpdatePortfolio("portfolio.ages.shareCapital", rows)}
                  />
                </>,
              ]}
            />
          </div>
        )}

        {/* ===== SAVINGS ===== */}
        {isSelected(COVERAGE_BASIS.SAVINGS) && (
          <div className="col-span-12 space-y-1">
            <span className="block text-center text-primary text-[18px] font-poppins-medium">TOTAL SAVINGS</span>
            <Tabs
              headers={["YEAR", "AGE"]}
              contents={[
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddSavingsYear}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "Years", key: "years", number: true },
                      { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true },
                      { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true },
                      { header: "Total Amount", key: "totalAmount", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.years.savings", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.years.savings}
                    onChange={(rows) => onUpdatePortfolio("portfolio.years.savings", rows)}
                  />
                </>,
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddSavingsAge}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "AGE FROM", key: "ageFrom", number: true },
                      { header: "AGE TO", key: "ageTo", number: true },
                      { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true },
                      { header: "TOTAL SAVINGS", key: "totalSavingsCapital", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.ages.savings", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.ages.savings}
                    onChange={(rows) => onUpdatePortfolio("portfolio.ages.savings", rows)}
                  />
                </>,
              ]}
            />
          </div>
        )}

        {/* ===== TIME DEPOSIT ===== */}
        {isSelected(COVERAGE_BASIS.TIME_DEPOSIT) && (
          <div className="col-span-12 space-y-1">
            <span className="block text-center text-primary text-[18px] font-poppins-medium">TOTAL TIME DEPOSIT</span>
            <Tabs
              headers={["YEAR", "AGE"]}
              contents={[
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddTimeDepositYear}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "Years", key: "years", number: true },
                      { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true },
                      { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true },
                      { header: "Total Amount", key: "totalAmount", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.years.timeDeposit", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.years.timeDeposit}
                    onChange={(rows) => onUpdatePortfolio("portfolio.years.timeDeposit", rows)}
                  />
                </>,
                <>
                  <Button classNames="block ms-auto mb-4" onClick={onAddTimeDepositAge}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline text-primary" />
                      <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                    </div>
                  </Button>
                  <EditableTable
                    columns={[
                      { header: "AGE FROM", key: "ageFrom", number: true },
                      { header: "AGE TO", key: "ageTo", number: true },
                      { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true },
                      { header: "TOTAL TIME DEPOSIT", key: "totalTimeDepositCapital", number: true, formatInput: true },
                      {
                        key: "",
                        header: "",
                        align: "center",
                        className: "text-[14px] font-[500] w-[50px]",
                        render: (_: any, index: number) => (
                          <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveItem("portfolio.ages.timeDeposit", index)}>
                            <PiMinusCircle className="inline text-primary" />
                          </Button>
                        ),
                      },
                    ]}
                    rows={portfolio.ages.timeDeposit}
                    onChange={(rows) => onUpdatePortfolio("portfolio.ages.timeDeposit", rows)}
                  />
                </>,
              ]}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PortfolioSection;
