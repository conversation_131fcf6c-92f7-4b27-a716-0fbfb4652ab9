import React from "react";

interface ProjectionTableProps {
  values: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const ProjectionTable: React.FC<ProjectionTableProps> = ({ values, handleChange }) => {
  const projectionData = [
    {
      projection: "Net Premium",
      value: values.totalPremiumNetRate,
      key: "projection.totalPremiumNetRate",
    },
    {
      projection: "Gross Premium",
      value: values.totalPremiumGrossRate,
      key: "projection.totalPremiumGrossRate",
    },
    {
      projection: "No. of Claims",
      value: values.numberOfClaims,
      key: "projection.numberOfClaims",
    },
    {
      projection: "Amount of Claims",
      value: values.amountOfClaims,
      key: "projection.amountOfClaims",
    },
    {
      projection: "Claims Ratio",
      value: values.claimsRatio,
      key: "projection.claimsRatio",
    },
  ];

  return (
    <div className="border border-gray/10 rounded-md min-w-lg w-full">
      <table className="w-full border-collapse border border-zinc-200 rounded">
        <thead className="bg-primary text-white">
          <tr>
            <th className="border border-zinc-200 px-4 py-2 text-left">PROJECTION</th>
            <th className="border border-zinc-200 px-4 py-2 text-left text-xs"></th>
          </tr>
        </thead>
        <tbody className="text-sm">
          {projectionData.map((row, index) => (
            <tr key={index}>
              <td className="border border-zinc-200 px-4 py-2">{row.projection}</td>
              <td className="border border-zinc-200 px-4 py-2" colSpan={2}>
                <input type="number" className="w-full border border-zinc-200 rounded px-2 py-1" name={row.key} value={row.value} onChange={handleChange} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProjectionTable;
