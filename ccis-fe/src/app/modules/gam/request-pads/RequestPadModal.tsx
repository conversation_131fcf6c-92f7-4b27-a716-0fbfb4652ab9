import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import { TOption } from "./TableFilter";
import { FormikProps } from "formik";
import { ICreatePadRequest } from "@interface/gam-request-pads";
import CustomTextField from "../components/CustomTextField";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IUser } from "@interface/user.interface";

type RequestPadModalProps = {
  isFormOpen: boolean;
  handleToggleFormModal: () => void;
  divisionOptions: TOption[];
  formik: FormikProps<ICreatePadRequest>;
  handleSearchUser: (event: ChangeEvent<HTMLInputElement>) => void;
  data: IUser[];
  remainingPads: number;
  typeOptions: TOption[];
  areaOptions: TOption[];
};

const RequestPadModal = ({ isFormOpen, handleToggleFormModal, divisionOptions, formik, handleSearchUser, data, remainingPads = 0, typeOptions, areaOptions }: RequestPadModalProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const lastSeries = useSelector((state: RootState) => state.gamPadRequest.lastSeries.data);
  const { postGamPadRequest } = useSelector((state: RootState) => state.gamPadRequest);
  const calculateSeriesTo = () => {
    const pad = 50;

    if (formik.values.numberOfPads > 0) {
      formik.setFieldValue("seriesTo", formik.values.numberOfPads * pad + lastSeries?.seriesNo);
    } else {
      formik.setFieldValue("seriesTo", 0);
    }
  };

  const setFormikValues = () => {
    formik.setFieldValue("seriesFrom", lastSeries?.seriesNo + 1);
  };

  const handleClick = () => {
    setIsEditing(true);
    // Delay to ensure state updates before focus
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const handleBlur = () => {
    if (typeof formik.values.numberOfPads === "string") {
      formik.setFieldValue("numberOfPads", 0);
    }
    setIsEditing(false);
  };

  useEffect(() => {
    calculateSeriesTo();
  }, [formik.values.numberOfPads]);

  useEffect(() => {
    if (isFormOpen) {
      formik.resetForm();
      setFormikValues();
    }
  }, [isFormOpen]);

  useEffect(() => {
    if (postGamPadRequest.success) {
      handleToggleFormModal();
    }
  }, [postGamPadRequest]);

  return (
    <Modal title="Add Request" modalContainerClassName="max-w-4xl" titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <form onSubmit={formik.handleSubmit}>
        <div className="w-full flex flex-col gap-10">
          {/* Pads container */}
          <div className="flex items-center justify-between gap-4">
            <div className="border-2 rounded-3xl border-[#042882] h-40 flex-1">
              <div className="h-full w-full flex flex-col items-center justify-center">
                <h3 className="font-semibold text-4xl text-[#042882]">{remainingPads}</h3>
                <span className="text-xl text-[#47474780]">Number of Remaining Pads</span>
              </div>
            </div>
            <div className="border-2 rounded-3xl border-[#042882] h-40 flex-1 cursor-pointer" onClick={handleClick}>
              <div className="h-full w-full flex flex-col items-center justify-center px-4">
                {isEditing ? (
                  <input
                    name="numberOfPads"
                    ref={inputRef}
                    type="number"
                    className="text-4xl text-center text-[#042882] font-bold focus:outline-none"
                    onBlur={handleBlur}
                    value={formik.values.numberOfPads}
                    onChange={formik.handleChange}
                  />
                ) : (
                  <h3 className="font-semibold text-4xl text-[#042882]">{formik.values.numberOfPads}</h3>
                )}
                <span className="text-xl text-[#47474780]">Number of Requested Pads</span>
              </div>
            </div>
          </div>

          {/* Textfields */}
          <div className="flex gap-4 ">
            {/* inputs left side*/}
            <div className="flex flex-col gap-4 flex-1">
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Division:</span>
                <Select
                  name="divisionId"
                  size="sm"
                  options={divisionOptions}
                  value={formik.values.divisionId}
                  onChange={(e) => {
                    const { name, value } = e.target;
                    formik.setFieldValue(name, parseInt(value));
                  }}
                />
              </div>
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Area:</span>
                <Select name="areaId" size="sm" disabled options={areaOptions} value={areaOptions.find((opt) => opt.value === formik.values.areaId)?.value} onChange={formik.handleChange} />
              </div>
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Series From:</span>
                <TextField name="seriesFrom" placeholder={"Series From"} size="sm" variant="primary" disabled value={formik.values.seriesFrom} onChange={formik.handleChange} />
              </div>
            </div>

            {/* inputs right side*/}
            <div className="flex flex-col gap-4 flex-1">
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Type:</span>
                <Select name="formTypeId" size="sm" options={typeOptions} value={formik.values.formTypeId} onChange={formik.handleChange} disabled />
              </div>
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Requested To:</span>
                <CustomTextField
                  suggestionOptions={data} // Array of the data
                  getOptionLabel={(u) => `${u.firstname} ${u.lastname}`} // displayed in the textfield
                  getOptionValue={(u) => u.id} // Value of selected data
                  name="releasedTo"
                  value={formik.values.releasedTo !== 0 ? formik.values.releasedTo : ""}
                  onChange={formik.handleChange}
                  onInputChange={handleSearchUser}
                  placeholder="Search User"
                  variant="primary"
                  size="sm"
                />
              </div>
              <div className="flex gap-4 items-center">
                <span className="w-36 text-sm">Series to:</span>
                <TextField name="seriesTO" placeholder={"Division"} size="sm" variant="primary" disabled value={formik.values.seriesTo} onChange={formik.handleChange} />
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex items-center justify-center w-full">
            <div className="w-[550px] flex gap-2">
              <Button variant="secondary" isSubmitting={postGamPadRequest.loading} classNames="w-full" onClick={handleToggleFormModal}>
                Cancel
              </Button>
              <Button variant="primary" isSubmitting={postGamPadRequest.loading} classNames="w-full" type="submit">
                Submit
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default RequestPadModal;
