import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import DatePicker from "@components/Datepicker/Datepicker";
import CustomTextField from "@modules/gam/components/CustomTextField";
import { RootState } from "@state/store";
import Loader from "@components/Loader";
import { FC, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { ITicketDepartmentUsers } from "@interface/departmental-ticketing-interface";

type User = {
  id: number;
  firstname: string;
  middlename?: string;
  lastname: string;
  roles: Array<{
    id: number;
    name: string;
    description: string;
  }>;
  department: {
    id: number;
    departmentName: string;
    departmentCode: string;
  };
};

type AssignUserModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // Optional callback for successful assignment
  ticketId?: number; // Optional ticket ID if needed for API call
  handleFetchTicketByID: () => void; // Optional function to fetch ticket by ID
};

const AssignUserModal: FC<AssignUserModalProps> = ({ isOpen, onClose, onSuccess, handleFetchTicketByID }) => {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [completionDate, setCompletionDate] = useState<Date | null>(new Date());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [users, setUsers] = useState<ITicketDepartmentUsers[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState<boolean>(false);

  const currentUserDepartmentId = useSelector((state: RootState) => state?.auth?.user?.data?.department?.id);
  const currentTicketID = useParams();
  const ticketId = currentTicketID.id;
  const { getDepartmentUsers, postAssignTicketUser } = useTicketActions();
  const departmentUsers = useSelector((state: RootState) => state?.departmentalTicketing?.departmentUsers);

  useEffect(() => {
    if (isOpen && currentUserDepartmentId) {
      setIsLoadingUsers(true);
      getDepartmentUsers({
        departmentId: currentUserDepartmentId,
        relations: "roles|department",
      });
    }
  }, [isOpen, currentUserDepartmentId]);

  useEffect(() => {
    if (departmentUsers && Array.isArray(departmentUsers)) {
      setUsers(departmentUsers);
      setIsLoadingUsers(false);
    }
  }, [departmentUsers]);

  const handleAssignUser = async () => {
    if (!selectedUserId || !selectedUser) return;

    try {
      setIsLoading(true);
      await postAssignTicketUser({ ticketId: Number(ticketId), userId: selectedUserId });
      onSuccess?.();
      handleCancel();
    } catch (error) {
      console.error("Failed to assign user:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedUserId(null);
    setSelectedUser(null);
    setStartDate(new Date());
    setCompletionDate(new Date());
    setIsLoading(false);
    setUsers([]);
    onClose();
    handleFetchTicketByID();
  };

  const getUserLabel = (user: User) => {
    const fullName = `${user.firstname} ${user.middlename ? user.middlename + " " : ""}${user.lastname}`.trim();
    const roleName = user.roles.length > 0 ? user.roles[0].description || user.roles[0].name : "No Role";
    return `${fullName} - ${roleName}`;
  };

  const getUserValue = (user: User) => user.id;

  const handleUserSelection = (e: any) => {
    const selectedUser = e.target.item as User;
    const selectedId = e.target.value as number;

    setSelectedUserId(selectedId);
    setSelectedUser(selectedUser);
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} showCloseButton={false} showHeader={false} modalContainerClassName="max-w-lg" className="backdrop-blur-sm">
      {isLoadingUsers ? (
        <div className="flex flex-1 w-full items-center justify-center">
          <Loader />
        </div>
      ) : (
        <div className="pt-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-3">Assign User</h2>
            <p className="text-sm text-gray-600 leading-relaxed">Please assign a user to handle this request/issues and resolve the ticket</p>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <DatePicker label="Start Date :" value={startDate} onChange={setStartDate} />
            <DatePicker label="Completion Date :" value={completionDate} onChange={setCompletionDate} />
          </div>

          <div className="mb-6">
            <CustomTextField
              suggestionOptions={users}
              getOptionLabel={getUserLabel}
              getOptionValue={getUserValue}
              value={selectedUserId || ""}
              onChange={handleUserSelection}
              placeholder={isLoadingUsers ? "Loading users..." : "Search and select a user..."}
              disabled={isLoadingUsers}
              className="w-full"
              name="selectedUser"
            />
          </div>

          <div className="flex gap-4 justify-end">
            <Button type="button" onClick={handleCancel} variant="custom" classNames="w-full">
              Cancel
            </Button>
            <Button type="button" onClick={handleAssignUser} classNames="w-full mt-4 bg-primary hover:bg-primary-dark text-white text-xs" disabled={!selectedUserId || isLoading || isLoadingUsers}>
              {isLoading ? "Assigning..." : "Assign"}
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default AssignUserModal;
