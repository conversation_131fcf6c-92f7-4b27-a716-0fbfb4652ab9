import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextArea from "@components/form/TextArea";
import Loader from "@components/Loader";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

interface ForwardTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticketId: number;
}

export default function ForwardTicketModal({ isOpen, onClose, ticketId }: ForwardTicketModalProps) {
  const { getDepartments, postForwardTicket } = useTicketActions();

  // Access departments from state
  const departmentsState = useSelector((state: any) => state?.departmentalTicketing?.getDepartments);
  const availableDepartments = departmentsState?.data || [];

  // Access forward ticket state for loading/success handling
  const forwardTicketState = useSelector((state: any) => state?.departmentalTicketing?.postTicketForward);

  // Form state
  const [selectedDepartment, setSelectedDepartment] = useState<number | string>("");
  const [remarks, setRemarks] = useState<string>("");

  const fetchAvailableDepartments = () => {
    getDepartments();
  };

  useEffect(() => {
    if (isOpen) {
      fetchAvailableDepartments();
      // Reset form when modal opens
      setSelectedDepartment("");
      setRemarks("");
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedDepartment) {
      // Handle validation error - you might want to show an error message
      return;
    }

    const payload = {
      ticketId,
      departmentId: Number(selectedDepartment),
      remarks: remarks,
    };

    postForwardTicket(payload);
  };

  // Handle department selection
  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartment(e.target.value);
  };

  // Handle remarks change
  const handleRemarksChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setRemarks(e.target.value);
  };

  // Close modal on successful forward
  useEffect(() => {
    if (forwardTicketState?.success) {
      onClose();
    }
  }, [forwardTicketState?.success, onClose]);

  // Transform departments data for Select component
  const departmentOptions = availableDepartments.map((dept: any) => ({
    value: dept.id,
    text: dept.departmentName,
    // You can also include the department code if needed
    // text: `${dept.departmentName} (${dept.departmentCode})`
  }));

  return (
    <Modal title="Forward Ticket" modalContainerClassName="max-w-lg" titleClass="text-gray-900 text-xl font-semibold" isOpen={isOpen} onClose={onClose}>
      {departmentsState?.loading ? (
        <div className="flex flex-1 w-full items-center justify-center">
          <Loader />
        </div>
      ) : (
        <form className="w-full" onSubmit={handleSubmit}>
          <p className="text-gray-600 text-sm mb-8">Select the involved department to assist in resolving or completing this ticket</p>

          <div className="mb-8">
            <div className="space-y-2">
              <Select placeholder="--- Select ---" required options={departmentOptions} value={selectedDepartment} onChange={handleDepartmentChange} />
            </div>
          </div>

          <div className="flex-none w-full mb-14 mt-4">
            <label className="block mb-2 text-sm font-medium">Remarks</label>
            <TextArea className="w-full" placeholder="Enter remarks" name="remarks" value={remarks} onChange={handleRemarksChange} />
          </div>

          <div className="flex gap-3 mt-8">
            <Button type="button" onClick={onClose} variant="custom">
              Cancel
            </Button>
            <Button type="submit" classNames="bg-primary hover:bg-primary-dark text-white text-xs w-full mt-4">
              Submit
            </Button>
          </div>
        </form>
      )}
    </Modal>
  );
}
