import Typography from "@components/common/Typography";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import { Fragment } from "react";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import Loader from "@components/Loader";

const ProductDetails = () => {

    const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
    const loading = useSelector((state: RootState) => state.products.getRevisionDetails.loading);

    const standard =
        revisionDetails?.commission?.commissionDetails?.filter(
            (rowValue) =>
                rowValue.commissionAgeType?.name?.toLowerCase() !== "standard"
        ) ?? [];

    return (
        <div className="flex flex-1 flex-col w-full h-full p-2">
            {loading && (
                <div className="flex flex-1 justify-center items-center h-full">
                    <Loader />
                </div>
            )}
            {!loading && revisionDetails &&
                (
                    <Fragment>
                        <div className="flex flex-1 flex-col justify-start items-start px-4">
                            <div className="flex w-full justify-center mt-4">
                                <Typography className="font-poppins-semibold">{revisionDetails.product.name}</Typography>
                            </div>
                            <div className="flex flex-col justify-start mt-4 mx-2">
                                <Typography className="font-poppins-semibold">Product Description</Typography>
                                <Typography className="ml-4 mt-2">{revisionDetails.product.description}</Typography>
                            </div>
                            <div className="flex flex-col w-full justify-start mx-2 mt-4">
                                {revisionDetails?.productGuidelines?.map((value, gIndex) => {
                                    return (
                                        <div
                                            key={`guideline-${gIndex}`}
                                            className="flex flex-1 flex-col mb-10"
                                        >
                                            <Typography className="text-[18px] mb-1 font-poppins-semibold">
                                                {value.label}
                                            </Typography>
                                            {value.productGuideline.map((pgValue, pgIndex) => {
                                                let listValue;
                                                let tableValue;
                                                if (pgValue.type === "list") {
                                                    listValue = pgValue.value as IGuidelineContent[];
                                                }

                                                if (pgValue.type === "table") {
                                                    tableValue = pgValue.value as IGuidelineContentTable;
                                                }

                                                return (
                                                    <div key={`pg-${pgIndex}`} className="text-justify">
                                                        {pgValue.type === "textfield" && (
                                                            <Fragment>
                                                                <Typography className="ml-4 mt-4 text-justify">
                                                                    {pgValue.value as string}
                                                                </Typography>
                                                            </Fragment>
                                                        )}
                                                        {pgValue.type === "list" && (
                                                            <Fragment>
                                                                <Typography className="ml-4 mt-4 text-justify">
                                                                    {pgValue.label}
                                                                </Typography>
                                                                <ul className="list-disc ml-12">
                                                                    {listValue &&
                                                                        listValue.map((listValue, listIndex) => {
                                                                            return (
                                                                                <li
                                                                                    key={`listItem-${listIndex}`}
                                                                                    className="mt-4"
                                                                                >
                                                                                    <Typography className="text-justify">
                                                                                        {listValue.value as string}
                                                                                    </Typography>
                                                                                </li>
                                                                            );
                                                                        })}
                                                                </ul>
                                                            </Fragment>
                                                        )}
                                                        {pgValue.type === "texteditor" && (
                                                            <Fragment>
                                                                <div
                                                                    className="ml-10 mt-10 text-black"
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: pgValue.value ?? "",
                                                                    }}
                                                                ></div>
                                                            </Fragment>
                                                        )}
                                                        {pgValue.type === "table" && (
                                                            <Fragment>
                                                                <div className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                                                                    <table className="table border-[1px]">
                                                                        <thead className="table-header-group">
                                                                            <tr>
                                                                                {tableValue?.columns?.map(
                                                                                    (cValue, cIndex) => {
                                                                                        return (
                                                                                            <td
                                                                                                key={`col-${cIndex}`}
                                                                                                className="table-cell border-[1px]"
                                                                                            >
                                                                                                <Typography className="font-semibold text-xs">
                                                                                                    {cValue.value as string}
                                                                                                </Typography>
                                                                                            </td>
                                                                                        );
                                                                                    }
                                                                                )}
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {tableValue?.rows?.map(
                                                                                (rValue, rIndex) => {
                                                                                    return (
                                                                                        <tr key={`row-${rIndex}`}>
                                                                                            {rValue.map((cell, cellIndex) => {
                                                                                                return (
                                                                                                    <td
                                                                                                        className="border-[1px] text-xs"
                                                                                                        key={`cell-${cellIndex}`}
                                                                                                    >
                                                                                                        <Typography>
                                                                                                            {cell.value as string}
                                                                                                        </Typography>
                                                                                                    </td>
                                                                                                );
                                                                                            })}
                                                                                        </tr>
                                                                                    );
                                                                                }
                                                                            )}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </Fragment>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    );
                                })}
                            </div>
                            <div className="flex flex-col w-full justify-start mx-2 mt-2">
                                {revisionDetails?.commission && (
                                    <Fragment>
                                        <Typography className="font-poppins-semibold" size="md">Commission Structure</Typography>
                                        <Fragment>
                                            <Typography size="sm" className="ml-4 mt-4">
                                                {parseFloat(
                                                    revisionDetails?.commission?.maximumDisposableRate ??
                                                    ""
                                                ).toFixed(2)}
                                                % Maximum Disposable Commission - Standard Rate
                                            </Typography>
                                            <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                                                <table className="table overflow-scroll">
                                                    <thead>
                                                        <tr>
                                                            <td className="table-cell border-[1px] text-center text-xs">
                                                                Type
                                                            </td>
                                                            <td className="table-cell border-[1px] text-center text-xs">
                                                                Age Type
                                                            </td>
                                                            {standard.length > 0 && (
                                                                <Fragment>
                                                                    <td className="table-cell border-[1px] text-center text-xs">
                                                                        Age From
                                                                    </td>
                                                                    <td className="table-cell border-[1px] text-center text-xs">
                                                                        Age To
                                                                    </td>
                                                                </Fragment>
                                                            )}
                                                            <td className="table-cell border-[1px] text-center text-xs">
                                                                Rate
                                                            </td>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {revisionDetails?.commission.commissionDetails?.map(
                                                            (rowValue, rowIndex) => {
                                                                return (
                                                                    <tr key={`commissionDetailsRow-${rowIndex}`}>
                                                                        <td className="table-cell border-[1px] text-xs">
                                                                            {rowValue?.commissionType?.commissionName}
                                                                        </td>
                                                                        <td className="table-cell border-[1px] text-xs">
                                                                            {rowValue?.commissionAgeType?.name}
                                                                        </td>
                                                                        {standard.length > 0 && (
                                                                            <Fragment>
                                                                                <td className="table-cell border-[1px] text-center text-xs">
                                                                                    {rowValue.ageFrom}
                                                                                </td>
                                                                                <td className="table-cell border-[1px] text-center text-xs">
                                                                                    {rowValue.ageTo}
                                                                                </td>
                                                                            </Fragment>
                                                                        )}
                                                                        <td className="table-cell border-[1px] text-center text-xs">
                                                                            {rowValue.rate
                                                                                ? parseFloat(
                                                                                    rowValue.rate.toString()
                                                                                ).toFixed(0)
                                                                                : ""}
                                                                            %
                                                                        </td>
                                                                    </tr>
                                                                );
                                                            }
                                                        )}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </Fragment>
                                    </Fragment>
                                )}
                            </div>
                        </div>
                    </Fragment>
                )
            }
        </div>
    )
}

export default ProductDetails