import { FC, useEffect } from "react";
import Typography from "@components/common/Typography";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ProductGuidelineType, ProposalAerGuidelines } from "@enums/proposalAerGuidelines";

type TProps = {
  approvedAERDetails: any;
  productGuidelines?: any[];
};

const CLSPGuidelines: FC<TProps> = ({ approvedAERDetails, productGuidelines }) => {
  // const premiumRate = approvedAERDetails?.quotation?.clspBenefits?.[0]?.rate ?? 1;
  const { getContestability } = useContestabilityActions();
  const contestability = useSelector((state: RootState) => state.contestability.getContestability);
  const quotation = approvedAERDetails?.quotation ?? approvedAERDetails?.proposable?.quotation;
  const clspBenefits = quotation?.clspBenefits ?? [];
  const premiumRate = clspBenefits?.[0]?.rate ?? 1;

  const premiumComputationHTML = `
    <div style="text-align: center;">
      <ul style="display: inline-block; text-align: left;">
        <li>Annual Premium is <i>one percent ${premiumRate}%</i> of the insured share capital. Premium calculation is on annual basis.</li>
      </ul>
      <div>Annual Premium = Amount of Insured Share Capital X ${premiumRate}%</div>
      <div>ANNEX A</div>
    </div>
  `;

  useEffect(() => {
    getContestability();
  }, []);

  return (
    <>
      {productGuidelines?.map((value: any, gIndex: number) => {
        if (value.label.includes(ProposalAerGuidelines.PremiumComputation)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-1 flex-col mb-6">
              <Typography className="text-[18px] mb-1 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.Texteditor || pgValue.type === "texteditor") {
                  // Always use premiumComputationHTML for texteditor in Premium Computation section
                  return (
                    <div
                      key={`pg-${pgIndex}`}
                      className="text-justify text-wrap px-4"
                      dangerouslySetInnerHTML={{
                        __html: premiumComputationHTML,
                      }}
                    />
                  );
                }

                if (pgValue.type === ProductGuidelineType.Table || pgValue.type === "table") {
                  return (
                    <div key={`pg-${pgIndex}`} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                      <table className="table border-[1px]">
                        <thead className="table-header-group">
                          <tr>
                            {pgValue.value?.columns?.map((col: any, colIndex: number) => (
                              <td key={`col-${colIndex}`} className="table-cell border-[1px]">
                                <Typography className="font-semibold text-xs text-primary">{col.value}</Typography>
                              </td>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {pgValue.value?.rows?.map((row: any[], rowIndex: number) => (
                            <tr key={`row-${rowIndex}`}>
                              {row.map((cell: any, cellIndex: number) => (
                                <td key={`cell-${cellIndex}`} className="border-[1px] text-xs">
                                  <Typography>{cell.value}</Typography>
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  );
                }

                if (pgValue.type === ProductGuidelineType.TextField || pgValue.type === "textfield") {
                  return (
                    <Typography key={`pg-${pgIndex}`} className="text-justify mt-4">
                      {pgValue.value}
                    </Typography>
                  );
                }

                if (pgValue.type === ProductGuidelineType.List || pgValue.type === "list") {
                  return (
                    <div key={`pg-${pgIndex}`} className="mt-4">
                      <Typography className="font-poppins-semibold">{pgValue.label}</Typography>
                      <ul className="list-disc ml-6">
                        {pgValue.value?.map((item: any, listIndex: number) => (
                          <li key={`listItem-${listIndex}`}>
                            <Typography>{item.value}</Typography>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                }

                return null;
              })}
            </div>
          );
        }
        if (value.label.includes(ProposalAerGuidelines.ScheduleOfPremiums)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.TextField || pgValue.type === "textfield") {
                  return (
                    <Typography key={`pg-${pgIndex}`} className="text-justify mt-2">
                      {pgValue.value}
                    </Typography>
                  );
                }

                if (pgValue.type === ProductGuidelineType.Table || pgValue.type === "table") {
                  return (
                    <div key={`pg-${pgIndex}`} className="overflow-x-auto mt-4">
                      <table className="table border-[1px] w-full">
                        <thead className="bg-amber-300">
                          <tr>
                            <th className="border-[1px] text-xs font-poppins-semibold text-center">Age</th>
                            <th className="border-[1px] text-xs font-poppins-semibold text-center">
                              <p>Coverage</p>( Share Capital Aggregate limit){" "}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {clspBenefits.map((benefit: any, benefitIndex: number) => (
                            <tr key={`benefit-${benefitIndex}`}>
                              <td className="border-[1px] text-sm text-center">
                                {benefit.ageFrom} - {benefit.ageTo}
                              </td>
                              <td className="border-[1px] text-sm text-center">1,000.00 to {new Intl.NumberFormat("en-US").format(benefit.maximumCoverageAmount)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  );
                }

                return null;
              })}
            </div>
          );
        }
        if (value.label.includes(ProposalAerGuidelines.GeneralProvisions)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.List) {
                  return (
                    <div key={`pg-${pgIndex}`} className="mt-4">
                      <Typography className="font-poppins-semibold">{pgValue.label}</Typography>
                      <ul className="list-disc ml-6">
                        {pgValue.value?.map((item: any, listIndex: number) => {
                          // Replace GROUP condition1
                          if (item.tag === "condition1") {
                            return (
                              <li key={`listItem-${listIndex}`}>
                                <Typography>
                                  1. 100% participation of all existing members of the cooperative or a minimum of 1300 enrollees in an "all-in" provision provided that:
                                  <br />
                                  a. the member is included in the list submitted to CLIMBS as basis for evaluation
                                  <br />
                                  b. they are enrolled at the same time on the FIRST REMITTANCE only ONE TIME ENROLLMENT.
                                  <br />
                                  Failure to meet the minimum participation requirement may result in a premium refund and re-evaluation if they wish to re-enrol.
                                </Typography>
                              </li>
                            );
                          }

                          // Replace INDIVIDUAL entryAge
                          if (item.tag === "entryAge") {
                            const ageFrom = clspBenefits?.[0]?.ageFrom ?? "18";
                            const ageTo = clspBenefits?.[0]?.ageTo ?? "65";
                            return (
                              <li key={`listItem-${listIndex}`}>
                                <Typography>{`Entry age is ${ageFrom} - ${ageTo} years old`}</Typography>
                              </li>
                            );
                          }

                          // Default rendering for other list items
                          return (
                            <li key={`listItem-${listIndex}`}>
                              <Typography>{item.value}</Typography>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  );
                }

                if (pgValue.type === ProductGuidelineType.TextField) {
                  return (
                    <Typography key={`pg-${pgIndex}`} className="text-justify mt-4">
                      {pgValue.value}
                    </Typography>
                  );
                }

                return null;
              })}
            </div>
          );
        }
        if (value.label.includes(ProposalAerGuidelines.OtherInsuranceProvisions)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.List) {
                  return (
                    <div key={`pg-${pgIndex}`} className="mt-4">
                      <Typography className="font-poppins-semibold">{pgValue.label}</Typography>
                      <ul className="list-disc ml-6">
                        {pgValue.value?.map((item: any, listIndex: number) => {
                          const isLastItem = listIndex === pgValue.value.length - 1;
                          const ageTo = clspBenefits?.[0]?.ageTo ?? "65";

                          // Replace the last list item only
                          if (isLastItem) {
                            return (
                              <li key={`listItem-${listIndex}`}>
                                <Typography>
                                  Any additional enrollees to this plan will be accepted and subject to entry age of not more than {ageTo} with a pro-rated premium based on the number of months
                                  covered. (refer “Annex A” - Sample Premium Computation)
                                </Typography>
                              </li>
                            );
                          }

                          // Render all other list items as-is
                          return (
                            <li key={`listItem-${listIndex}`}>
                              <Typography>{item.value}</Typography>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  );
                }

                return null;
              })}
            </div>
          );
        }
        if (value.label.includes(ProposalAerGuidelines.Contestability)) {
          const contestabilityId = quotation?.contestability;

          const contestabilityObject = contestability?.data?.find((item: any) => item.id === contestabilityId);
          const selectedContestability = contestabilityObject?.value || contestabilityObject?.label || "";

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                // Handle texteditor type (your current data structure)
                if (pgValue.type === "texteditor") {
                  // Check if contestability is "Waived with masterlist"
                  const isWaivedWithMasterlist = selectedContestability === "Waived with masterlist";

                  if (isWaivedWithMasterlist) {
                    const customContestabilityHTML = `
              <ul style="list-style-type: disc; margin-left: 20px;">
                <li>One (1) year contestability period for new & incoming members. However, waive contestability period for all members included in the Masterlist submitted and enrolled at the same time on the FIRST REMITTANCE.</li>
              </ul>

                <p>Members who fail to renew within the thirty (30) day grace period shall be considered as "new" and will automatically be subject to a one (1) year contestability period.</p>
            `;

                    return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: customContestabilityHTML }} />;
                  }

                  // Default rendering for other contestability values
                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pgValue.value ?? "" }} />;
                }

                return null;
              })}
            </div>
          );
        }

        // Default rendering for all other guidelines
        return (
          <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
            <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

            {value.productGuideline?.map((pg: any, pgIndex: number) => {
              if (pg.type === ProductGuidelineType.Texteditor || pg.type === "texteditor") {
                return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
              }
              if ((pg.type === ProductGuidelineType.TextField || pg.type === "textfield") && pg.value) {
                return (
                  <Typography key={pgIndex} className="text-justify mt-4">
                    {pg.value}
                  </Typography>
                );
              }

              // Fixed: Use ProductGuidelineType.List instead of "list"
              if (pg.type === ProductGuidelineType.List || pg.type === "list") {
                return (
                  <div key={pgIndex} className="mt-4">
                    <Typography className="font-poppins-semibold">{pg.label}</Typography>
                    <ul className="list-disc ml-6">
                      {pg.value?.map((item: any, idx: number) => (
                        <li key={idx}>
                          <Typography>{item.value}</Typography>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }

              // Add handling for Table type if needed
              if (pg.type === ProductGuidelineType.Table || pg.type === "table") {
                return (
                  <div key={pgIndex} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                    <table className="table border-[1px]">
                      <thead className="table-header-group">
                        <tr>
                          {pg.value?.columns?.map((col: any, colIndex: number) => (
                            <td key={`col-${colIndex}`} className="table-cell border-[1px]">
                              <Typography className="font-semibold text-xs text-primary">{col.value}</Typography>
                            </td>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {pg.value?.rows?.map((row: any[], rowIndex: number) => (
                          <tr key={`row-${rowIndex}`}>
                            {row.map((cell: any, cellIndex: number) => (
                              <td key={`cell-${cellIndex}`} className="border-[1px] text-xs">
                                <Typography>{cell.value}</Typography>
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                );
              }

              return null;
            })}
          </div>
        );
      })}
    </>
  );
};

export default CLSPGuidelines;
