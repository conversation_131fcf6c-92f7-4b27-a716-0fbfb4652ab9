import { FC, useEffect, useMemo, useState } from "react";
import Typography from "@components/common/Typography";
import { ProductGuidelineType, ProposalAerGuidelines } from "@enums/proposalAerGuidelines";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import Tabs from "@components/common/Tabs";
import ConditionOptionTab from "@modules/sales/fip-quotations/components/Tabs/ConditionOptionTab";

type Props = {
  approvedAERDetails: any;
  productGuidelines: any[];
};

const FIPGuidelines: FC<Props> = ({ approvedAERDetails, productGuidelines }) => {
  const { getContestability } = useContestabilityActions();
  const contestability = useSelector((state: RootState) => state?.contestability?.getContestability);
  let hasReplacedFirstTable = false;
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const quotation = approvedAERDetails?.quotation ?? approvedAERDetails?.proposable?.quotation;
  const fipPrincipalMember = quotation?.fipPrincipalMember ?? [];
  const optionTabs = fipPrincipalMember.reduce((acc: { option: number }[], item: any) => {
    if (!acc.some((entry) => entry.option === item.option)) {
      acc.push({ option: item.option });
    }
    return acc;
  }, []);

  const [_activeOption, setActiveOption] = useState<number | undefined>(optionTabs?.[0]?.option);
  const relationships = useMemo(() => {
    const dependents = quotation?.fipCoInsuredDependentBenefit ?? [];

    return dependents.map((item: any) => ({
      relationship: item.relationship,
      type: item.type,
      benefitId: item.benefitId,
      minimumAge: item.minimumAge,
      maximumAge: item.maximumAge,
      exitAge: item.exitAge,
      coverage: item.coverage,
      option: item.option,
      netPremium: quotation?.fipCoInsuredDependent?.filter((opt: any) => opt.option === item.option)?.map((i: any) => i.netPremium)?.[0],
    }));
  }, [approvedAERDetails]);

  useEffect(() => {
    getContestability();
    getProductBenefits({ filter: "" });
  }, []);
  return (
    <div className="flex flex-col space-y-6">
      {productGuidelines?.map((value: any, gIndex: number) => {
        if (
          [
            ProposalAerGuidelines.SalientFeatures,
            ProposalAerGuidelines.ClaimProcessFlow,
            ProposalAerGuidelines.InsuranceCoverageEffectivity,
            ProposalAerGuidelines.CFPScheduleOfPremium,
            ProposalAerGuidelines.UnderwritingProcedure,
          ].includes(value.label)
        ) {
          return null;
        }

        if (!value.productGuideline || value.productGuideline.length === 0) {
          return null;
        }
        if (value.label === ProposalAerGuidelines.ScheduleOfBenefits) {
          return (
            <div className="mt-6">
              <Tabs
                headers={optionTabs.map((opt: { option: number }) => `Option ${opt.option}`)}
                contents={optionTabs.map((opt: { option: number }) => {
                  const filteredRelationships = relationships.filter((rel: { option: number }) => rel.option === opt.option);

                  return (
                    <ConditionOptionTab
                      relationships={filteredRelationships}
                      optionNumber={opt.option}
                      quotationData={[approvedAERDetails]}
                      showPrincipalOnly={false}
                      headerBgClass="bg-amber-400"
                      headerTextClass="text-black"
                    />
                  );
                })}
                headerClass="w-1/2 mx-auto flex flex-row justify-center text-lg font-poppins-medium"
                fullWidthHeader={false}
                onTabChange={(tabIndex) => {
                  setActiveOption(optionTabs[tabIndex]?.option);
                }}
              />
            </div>
          );
        }

        if (value.label.includes(ProposalAerGuidelines.GeneralProvisions)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.List) {
                  return (
                    <div key={`pg-${pgIndex}`} className="mt-4">
                      <Typography className="font-poppins-semibold">{pgValue.label}</Typography>
                      <ul className="list-disc ml-6">
                        {pgValue.value?.map((item: any, listIndex: number) => {
                          if (item.tag === "condition1") {
                            return (
                              <li key={`listItem-${listIndex}`}>
                                <Typography>
                                  1. 100% participation of all existing members of the cooperative or a minimum of 1300 enrollees in an "all-in" provision provided that:
                                  <br />
                                  a. the member is included in the list submitted to CLIMBS as basis for evaluation
                                  <br />
                                  b. they are enrolled at the same time on the FIRST REMITTANCE only ONE TIME ENROLLMENT.
                                  <br />
                                  Failure to meet the minimum participation requirement may result in a premium refund and re-evaluation if they wish to re-enrol.
                                </Typography>
                              </li>
                            );
                          }

                          if (item.tag === "entryAge") {
                            const ageFrom = approvedAERDetails?.quotation?.fipBenefits?.[0]?.ageFrom ?? "18";
                            const ageTo = approvedAERDetails?.quotation?.fipBenefits?.[0]?.ageTo ?? "65";
                            return (
                              <li key={`listItem-${listIndex}`}>
                                <Typography>{`Entry age is ${ageFrom} - ${ageTo} years old`}</Typography>
                              </li>
                            );
                          }

                          return (
                            <li key={`listItem-${listIndex}`}>
                              <Typography>{item.value}</Typography>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  );
                }

                if (pgValue.type === ProductGuidelineType.Table) {
                  const individualReqIndex = value.productGuideline.findIndex((pg: any) => pg.label === "INDIVIDUAL ELIGIBILITY REQUIREMENTS");

                  const pgIsAfterEligibility = pgIndex > individualReqIndex;

                  const interveningItems = value.productGuideline.slice(individualReqIndex + 1, pgIndex);

                  const isExcludedDependentTable = interveningItems.some(
                    (item: any) =>
                      item.type === ProductGuidelineType.List && item.value?.some((v: any) => typeof v?.value === "string" && v.value.toLowerCase().includes("eligible dependents to enroll"))
                  );

                  const isFirstValidTableAfterEligibility =
                    !hasReplacedFirstTable &&
                    individualReqIndex !== -1 &&
                    pgIsAfterEligibility &&
                    !isExcludedDependentTable &&
                    interveningItems.findIndex((pg: any) => pg.type === ProductGuidelineType.Table) === -1;

                  if (isFirstValidTableAfterEligibility) {
                    hasReplacedFirstTable = true;
                    return (
                      <div key={`pg-${pgIndex}`} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                        <table className="table border-[1px]">
                          <thead className="table-header-group">
                            <tr>
                              <td className="table-cell border-[1px]">
                                <Typography className="font-semibold text-xs text-primary">Member Type</Typography>
                              </td>
                              <td className="table-cell border-[1px]">
                                <Typography className="font-semibold text-xs text-primary">Age Requirement</Typography>
                              </td>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td className="border-[1px] text-xs">
                                <Typography>Member (Principal Holder)</Typography>
                              </td>
                              <td className="border-[1px] text-xs">
                                <Typography>18 to 69 years old</Typography>
                              </td>
                            </tr>
                            <tr>
                              <td className="border-[1px] text-xs">
                                <Typography>Spouse & Parents</Typography>
                              </td>
                              <td className="border-[1px] text-xs">
                                <Typography>18 to 69 years old</Typography>
                              </td>
                            </tr>
                            <tr>
                              <td className="border-[1px] text-xs">
                                <Typography>Children/Siblings</Typography>
                              </td>
                              <td className="border-[1px] text-xs">
                                <Typography>4-17</Typography>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    );
                  }

                  // Default render for all other tables
                  return (
                    <div key={pgIndex} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                      <table className="table border-[1px]">
                        <thead className="table-header-group">
                          <tr>
                            {pgValue.value?.columns?.map((col: any, colIndex: number) => (
                              <td key={`col-${colIndex}`} className="table-cell border-[1px]">
                                <Typography className="font-semibold text-xs text-primary">{col.value}</Typography>
                              </td>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {pgValue.value?.rows?.map((row: any[], rowIndex: number) => (
                            <tr key={`row-${rowIndex}`}>
                              {row.map((cell: any, cellIndex: number) => (
                                <td key={`cell-${cellIndex}`} className="border-[1px] text-xs">
                                  <Typography>{cell.value}</Typography>
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  );
                }

                if (pgValue.type === ProductGuidelineType.TextField) {
                  return (
                    <Typography key={`pg-${pgIndex}`} className="text-justify mt-4">
                      {pgValue.value}
                    </Typography>
                  );
                }
                if (pgValue.type === ProductGuidelineType.Texteditor) {
                  let editorHtml = pgValue.value ?? "";

                  // Only replace if it contains "18 to 69" and NOT already "4 to 17"
                  if (editorHtml.includes("18 to 69") && !editorHtml.includes("4 to 17")) {
                    editorHtml = editorHtml.replace(/18 to 69/g, "4 to 17");
                  }

                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4 mt-4" dangerouslySetInnerHTML={{ __html: editorHtml }} />;
                }

                return null;
              })}
            </div>
          );
        }

        if (value.label.includes(ProposalAerGuidelines.Contestability)) {
          const contestabilityId = approvedAERDetails?.quotation?.contestability;
          const contestabilityObject = contestability?.data?.find((item: any) => item.id === contestabilityId);
          const selectedContestability = contestabilityObject?.value || contestabilityObject?.label || "";

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                // Handle texteditor type (your current data structure)
                if (pgValue.type === "texteditor") {
                  // Check if contestability is "Waived with masterlist"
                  const isWaivedWithMasterlist = selectedContestability === "Waived with masterlist";

                  if (isWaivedWithMasterlist) {
                    const customContestabilityHTML = `
              <ul style="list-style-type: disc; margin-left: 20px;">
                <li>One (1) year contestability period for new & incoming members. However, waive contestability period for all members included in the Masterlist submitted and enrolled at the same time on the FIRST REMITTANCE.</li>
              </ul>

                <p>Members who fail to renew within the thirty (30) day grace period shall be considered as "new" and will automatically be subject to a one (1) year contestability period.</p>
            `;

                    return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: customContestabilityHTML }} />;
                  }

                  // Default rendering for other contestability values
                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pgValue.value ?? "" }} />;
                }

                return null;
              })}
            </div>
          );
        }

        return (
          <div key={gIndex} className="mb-6">
            <Typography className="text-[18px] mb-2 font-poppins-semibold text-primary">{value.label}</Typography>
            {value.productGuideline.map((pg: any, pgIndex: number) => {
              if (pg.type === "texteditor") {
                return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
              }
              if (pg.type === "textfield") {
                return (
                  <Typography key={pgIndex} className="text-justify mt-2 px-4">
                    {pg.value}
                  </Typography>
                );
              }
              if (pg.type === ProductGuidelineType.Table) {
                return (
                  <div key={pgIndex} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                    <table className="table border-[1px]">
                      <thead className="table-header-group">
                        <tr>
                          {pg.value?.columns?.map((col: any, colIndex: number) => (
                            <td key={`col-${colIndex}`} className="table-cell border-[1px]">
                              <Typography className="font-semibold text-xs text-primary">{col.value}</Typography>
                            </td>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {pg.value?.rows?.map((row: any[], rowIndex: number) => (
                          <tr key={`row-${rowIndex}`}>
                            {row.map((cell: any, cellIndex: number) => (
                              <td key={`cell-${cellIndex}`} className="border-[1px] text-xs">
                                <Typography>{cell.value}</Typography>
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                );
              }
              if (pg.type === "list") {
                return (
                  <div key={pgIndex} className="mt-2 px-4">
                    <Typography className="font-poppins-semibold mb-1">{pg.label}</Typography>
                    <ul className="list-disc ml-6">
                      {pg.value?.map((item: any, idx: number) => (
                        <li key={idx}>
                          <Typography>{item.value}</Typography>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }
              return null;
            })}
          </div>
        );
      })}
    </div>
  );
};

export default FIPGuidelines;
