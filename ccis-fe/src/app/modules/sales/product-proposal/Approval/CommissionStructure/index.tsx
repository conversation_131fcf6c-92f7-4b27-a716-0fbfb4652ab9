import { FC, useState, useEffect, ChangeEvent, useRef } from "react";
import Button from "@components/common/Button";
import { FiPlus } from "react-icons/fi";
import Modal from "@components/common/Modal";
import CheckBox from "@components/form/CheckBox";
import { BiGridVertical } from "react-icons/bi";
import { IoIosClose } from "react-icons/io";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { useProductCommissionManagementActions } from "@state/reducer/utilities-product-commission";
import TextField from "@components/form/TextField";
import { VscPercentage } from "react-icons/vsc";
import { IProductProposal } from "@interface/product-proposal.interface";
import {ICommissionDetails } from "@interface/commission-structure.interface";
import { toast } from "react-toastify";
import { formatDateToMonthYear } from "@helpers/date";
import { postProductProposalCommissionService } from "@services/product-proposal/product-proposal.service";
import { showConfirmation } from "@helpers/prompt";
import { getTextStatusColor } from "@helpers/text";
import { capitalizeFirstLetterWords } from "@helpers/text";
import { useLocation } from "react-router-dom";
import { ICommissionDistributions } from "@interface/quotation.interface";

type Props = {
  data?: IProductProposal;
  onSuccess?: () => void;
};

type ProductProposalCommission = {
  id?: number;
  commissionName?: string;
  commissionPercentage?: number;
};

const CommissionStructure: FC<Props> = ({ data, onSuccess }) => {


  const location = useLocation();
  const proposalType = location.state?.type;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [create, setCreate] = useState<boolean>(false); //MODAL SA CREATE
  const [commissionTypes, setCommissionTypes] = useState<
    ProductProposalCommission[]
  >([]);
  const [totalCommission, setTotalCommission] = useState<number>(0);
  const [totalComissionRate, setTotalComissionRate] = useState<number>(0);
  const previousValues = useRef<Record<number, number>>({});
  const [inputKeys, setInputKeys] = useState<Record<number, number>>({});


  const productCommission = useSelector(
    (state: RootState) => state.utilitiesProductCommission.productCommission
  );

  const { getProductCommission } = useProductCommissionManagementActions();

  const [checkedItems, setCheckedItems] = useState<number[]>([]);

  const handleCheckboxChange = (itemId: number) => {
    setCheckedItems((prev) => {
      if (prev.includes(itemId)) {
        return prev.filter((id) => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  useEffect(() => {
    // Type guard to check if proposable is IProductRevisions
    if (data?.proposable && 'product' in data.proposable) {
      const latestApprovedRevision = (data.proposable as any)?.product?.productRevisions?.find((revision: any) => revision.approvalStatus === "APPROVED" && revision.commission);
      setTotalComissionRate(Number(latestApprovedRevision?.commission?.maximumDisposableRate) ?? 0);
    }
  }, [data]);

  useEffect(() => {
    if(proposalType === "standard"){
      // Type guard to check if proposable is IProductRevisions
      if (data?.proposable && 'commission' in data.proposable) {
        const newCommissionTypes = (data.proposable as any)?.commission?.commissionDetails?.filter(
          (item: ICommissionDetails) =>
            item?.commissionType?.id &&
            item?.commissionType?.commissionName &&
            item?.rate !== null
        )
        .map((item: ICommissionDetails) => ({
          id: item?.commissionType!.id,
          commissionName: item?.commissionType!.commissionName,
          commissionPercentage: Number(item.rate ?? 0.0),
        }));
        setCommissionTypes(newCommissionTypes ?? []);
        setCheckedItems(newCommissionTypes?.map((type: any) => type.id!) ?? []);
      }
    }

          if(proposalType === "custom"){
        console.log("custom", data?.proposable)
        // Type guard to check if proposable is IQuotationData
        const proposable = data?.proposable as any;
        if (proposable && proposable.quotation && 'quotationCommissionDistribution' in proposable.quotation) {
          const newCommissionTypes = proposable.quotation.quotationCommissionDistribution?.map((item: ICommissionDistributions) => ({
            id: typeof item?.commissionType === 'object' ? item?.commissionType?.id : item?.commissionTypeId,
            commissionName: typeof item?.commissionType === 'object' ? item?.commissionType?.commissionName : '',
            commissionPercentage: Number((item.rate ?? 0) * 100),
          }));
          setCommissionTypes(newCommissionTypes ?? []);
          setCheckedItems(newCommissionTypes?.map((type: any) => type.id!) ?? []);
        }
      }
  }, [data?.proposable]); 

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
  };

  const calculateTotalCommission = () => {
    return commissionTypes.reduce(
      (acc, curr) => acc + (curr.commissionPercentage ?? 0),
      0
    );
  };

  const isTotalCommissionGreaterThanMaximumDisposableRate = () => {
    return (
      calculateTotalCommission() >
      Number(totalComissionRate)
    );
  };

  const handleCommissionChange = (
    e: ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    const value = e.target.value === "" ? 0 : parseFloat(e.target.value);
    
    // Calculate total without the current field
    const totalCommissionValue = commissionTypes.reduce(
      (acc, curr) =>
        acc + (curr.id !== id ? curr.commissionPercentage ?? 0 : 0),
      0
    );
    
    const maxRate = Number(totalComissionRate);
    const newTotal = totalCommissionValue + value;
    
    // Prevent entering value that would exceed maximum disposable rate
    if (newTotal > maxRate) {
      toast.error(
        `Total commission value cannot exceed the maximum disposable rate of ${maxRate.toFixed(2)}%`
      );
      // Force input to revert by changing its key
      setInputKeys(prev => ({ ...prev, [id]: (prev[id] || 0) + 1 }));
      return;
    }
    
    // Update the previous value for next time
    previousValues.current[id] = value;
    
    setCommissionTypes((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, commissionPercentage: value } : item
      )
    );

    setTotalCommission(parseFloat(newTotal.toFixed(4)));
  };

  const handleSaveDraft = async () => {
    setIsLoading(true);
    const hasNullCommissionPercentage = commissionTypes.some(
      (item) => !item.commissionPercentage
    );

    if (isTotalCommissionGreaterThanMaximumDisposableRate()) {
      setIsLoading(false);
      return toast.error(
        `Total commission value is greater than the maximum disposable rate which is ${Number(
          totalComissionRate
        ).toFixed(2)}%`
      );
    }

    if (hasNullCommissionPercentage) {
      setIsLoading(false);
      return toast.error(
        "Please enter commission percentage for all commission types"
      );
    }
    if (!data?.id) return;

    const payload = {
      productProposalId: data.id,
      status: "DRAFT",
      commissionDetails: commissionTypes
        .filter((item) => item.id != null && item.commissionPercentage != null)
        .map((item) => ({
          commissionTypeId: item.id!,
          commissionPercentage: item.commissionPercentage!,
        })),
      totalCommission: totalCommission,
    };
    try {
      const { data } = await postProductProposalCommissionService(payload);
      if (data) {
        toast.success("Draft saved successfully");
        onSuccess?.();
      }
      setIsLoading(false);
    } catch (error) {
      toast.error("Failed to save draft");
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (checkedItems.length <= 0) return;
    if (data?.proposalApproval?.status !== "APPROVED") return;

    const hasNullCommissionPercentage = commissionTypes.some(
      (item) => !item.commissionPercentage
    );
    if (hasNullCommissionPercentage) {
      return toast.error(
        "Please enter commission percentage for all commission types"
      );
    }

    if (isTotalCommissionGreaterThanMaximumDisposableRate()) {
      setIsLoading(false);
      return toast.error(
        `Total commission value is greater than the maximum disposable rate which is ${Number(
          totalComissionRate
        ).toFixed(2)}%`
      );
    }

    const result = await showConfirmation(
      "Commission Structure",
      "Are you sure you want to submit this commission structure?"
    );

    if (result.isConfirmed) {
      setIsLoading(true);
      if (!data?.id) return;

      const payload = {
        productProposalId: data.id,
        status: "FOR_APPROVAL",
        commissionDetails: commissionTypes
          .filter(
            (item) => item.id != null && item.commissionPercentage != null
          )
          .map((item) => ({
            commissionTypeId: item.id!,
            commissionPercentage: item.commissionPercentage!,
          })),
        totalCommission: totalCommission,
      };
      try {
        const { data } = await postProductProposalCommissionService(payload);
        if (data) {
          toast.success("Draft saved successfully");
          onSuccess?.();
        }
        setIsLoading(false);
      } catch (error) {
        toast.error("Failed to save draft");
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (create) {
      const newCommissionType = checkedItems
        .map((id) => {
          const existingCommission = commissionTypes.find(
            (type) => type.id === id
          );
          const commissionData = productCommission.find(
            (data) => data.id === id
          );

          return {
            id: commissionData?.id as number,
            commissionName: commissionData?.commissionName as string,
            commissionPercentage: existingCommission?.commissionPercentage,
          };
        })
        .filter(Boolean) as ProductProposalCommission[];

      setCommissionTypes(newCommissionType); // <-- causes recursion when commissionTypes is used in the dependency array
    }
  }, [checkedItems, create /*commissionTypes <-- causes recursion */]);

  const isDisabled =
    ["FOR_APPROVAL"].includes(data?.commissionStatus as string) &&
    ["APPROVED"].includes(data?.proposalApproval?.status as string);

  useEffect(() => {
    getProductCommission({ filter: "" });
  }, []);

  // Update total commission whenever commissionTypes changes
  useEffect(() => {
    const newTotal = calculateTotalCommission();
    setTotalCommission(newTotal);
  }, [commissionTypes]);

  // Initialize previous values when commission types are loaded
  useEffect(() => {
    commissionTypes.forEach((item) => {
      if (item.id && item.commissionPercentage !== undefined) {
        previousValues.current[item.id] = item.commissionPercentage;
      }
    });
  }, [commissionTypes]);


  return (
    <div className="w-full">
      {create && (
        <Modal
          title="Select Commission Type"
          modalContainerClassName="max-w-5xl "
          titleClass="text-primary text-lg uppercase"
          isOpen={create}
          onClose={handleToggleCreateModal}
        >
          <>
            <div className="w-full flex min-h-[500px] h-full my-4 gap-2 ">
              <div className="w-full flex-col h-[500px]  border border-zinc-300   rounded-md p-6 ">
                {" "}
                Commission Types :
                <div className="flex flex-col h-[90%] divide-y divide-zinc-300 overflow-y-auto">
                  {productCommission.map((data: any) => (
                    <div
                      className="py-2 flex items-center justify-start "
                      key={data.id}
                    >
                      <CheckBox
                        value="choose"
                        checked={checkedItems.includes(data.id)}
                        onChange={() => handleCheckboxChange(data.id)}
                        className="mr-2"
                      />
                      {data?.commissionName}
                    </div>
                  ))}
                  {/* Test, fill with 20 items */}
                  {/* {Array.from({ length: 20 }, (_, index) => (
                    <div
                      className="py-2 flex items-center justify-start "
                      key={index}
                    >
                      <CheckBox
                        value="choose"
                        checked={checkedItems.includes(index)}
                        onChange={() => void(0)}
                        className="mr-2"
                      />
                      Commission Type {index + 1}
                    </div>
                  ))} */}
                </div>
              </div>
              <div className="w-full h-max  border  border-zinc-300 rounded-md ">
                <div className="w-full flex-col h-[500px] flex items-start p-6 ">
                  {" "}
                  Selected Commission Types :
                  <div className=" w-full flex flex-col h-[90%] gap-2 py-2 overflow-auto ">
                    {checkedItems.map((id) => {
                      const data = productCommission.find(
                        (type) => type.id === id
                      );
                      return (
                        <div className="w-full rounded-xl border border-zinc-200  p-3 bg-zinc-50 flex justify-around ">
                          <div className="flex items-center w-5/6 ">
                            {" "}
                            <BiGridVertical size={20} />
                            {data?.commissionName}
                          </div>
                          <div className="w-1/6 flex items-center justify-end ">
                            <IoIosClose
                              className="cursor-pointer hover:text-red-500 hover:bg-gray-100 rounded-md"
                              size={20}
                              onClick={() =>
                                handleCheckboxChange((data as any).id)
                              }
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            <div className="w-full flex justify-between ">
              <div>
                <Button
                  type="button"
                  variant="primary"
                  outline
                  onClick={() => setCheckedItems([])}
                >
                  {" "}
                  Remove All
                </Button>
              </div>
              <div className=" gap-2 flex">
                <Button type="button" classNames="w-48">
                  {" "}
                  Cancel
                </Button>
                <Button
                  type="button"
                  classNames="w-48"
                  variant="primary"
                  onClick={handleToggleCreateModal}
                >
                  {" "}
                  Apply
                </Button>
              </div>
            </div>
          </>
        </Modal>
      )}

      {/* DIRI MAG START ANG MGA CONTENTS */}
      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">
                {formatDateToMonthYear(data?.createdAt) ?? ""}
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">
                {data?.cooperative?.coopName}
              </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Status</div>
              <div className="w-2/3 text-black">
                <span
                  className={`${getTextStatusColor(
                    data?.commissionStatus ?? ""
                  )}`}
                >
                  {capitalizeFirstLetterWords(
                    data?.commissionStatus ?? "",
                    "_"
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="relative px-10 w-full">
        <div className="flex gap-2 mt-14 justify-between">
          <div>
            <div className="font-poppins-semibold">Commission Structure</div>
            <div className="text-xs text-zinc-400">
              Please input and manage your commission details.
            </div>
          </div>
        </div>
        <div className="w-full min-h-60 ">
          <div className="w-1/2">
            <div className="flex justify-between items-center border-b border-zinc-300 w-full pb-2">
              <div>
                Maximum Disposable Rate:{" "}
                <span className="font-poppins-semibold text-primary2">
                  {Number(
                    totalComissionRate
                  ).toFixed(2)}
                  %
                </span>
              </div>
              {["DRAFT", "FOR_REVISION", "PENDING"].includes(
                data?.commissionStatus as string
              ) &&
                ["APPROVED"].includes(
                  data?.proposalApproval?.status as string
                ) &&
                proposalType !== "custom" && (
                  <Button
                    variant="success"
                    classNames="btn border-0 w-24"
                    onClick={handleToggleCreateModal}
                    disabled={isLoading}
                  >
                    {" "}
                    <FiPlus size={20} />
                    Add
                  </Button>
                )}
            </div>
          </div>
          <div className="w-1/2  flex flex-col gap-4  p-4">
            {commissionTypes.map((item: any) => (
              <div
                key={item.id}
                className="flex  items-center justify-between w-full"
              >
                <div> {item.commissionName}</div>
                <div>
                  {" "}
                  <TextField
                    key={`commission-${item.id}-${inputKeys[item.id] || 0}`}
                    placeholder="Enter Commission"
                    type="number"
                    className="py-6 w-12"
                    size="sm"
                    rightIcon={<VscPercentage />}
                    value={item.commissionPercentage ?? 0}
                    onChange={(e) => handleCommissionChange(e, item.id)}
                    disabled={isLoading || isDisabled || proposalType === "custom"}
                  />
                </div>
              </div>
            ))}
            {commissionTypes.length !== 0 && (
              <div className="flex  items-center justify-between w-full border-y border-zinc-200 py-4">
                <div> Total</div>
                <div className="flex items-center gap-2">
                  <TextField
                    placeholder="Total Commission"
                    disabled
                    type="number"
                    className="py-6 w-12"
                    size="sm"
                    rightIcon={<VscPercentage />}
                    value={totalCommission}
                  />
                </div>
              </div>
            )}
            {["DRAFT", "FOR_REVISION", "PENDING"].includes(
              data?.commissionStatus as string
            ) &&
              ["APPROVED"].includes(
                data?.proposalApproval?.status as string
              ) && (
                <div className="flex gap-2 justify-end">
                  <Button
                    variant="secondary"
                    classNames="btn btn-sm border-0 w-32"
                    onClick={handleSaveDraft}
                    disabled={isLoading}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    variant="primary"
                    classNames="btn btn-sm border-0 w-32"
                    disabled={isLoading || checkedItems.length <= 0}
                    onClick={handleSubmit}
                  >
                    Submit
                  </Button>
                </div>
              )}
            {!["APPROVED"].includes(
              data?.proposalApproval?.status as string
            ) && (
              <div className="flex gap-2 justify-end text-xs text-primary2">
                You cannot submit this commission structure because the product
                proposal is not approved.
              </div>
            )}
          </div>
        </div>
        {data?.commissionStructure?.remarks && (
          <div className="w-full flex flex-col gap-2 mt-5">
            <h4 className="font-poppins-semibold uppercase">Remarks</h4>
            <p className="text-zinc-400">
              {data?.commissionStructure?.remarks}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommissionStructure;
