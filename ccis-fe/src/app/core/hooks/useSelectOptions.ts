import { useMemo } from "react";

type Option = { value: any; text: string };

type UseSelectOptionsProps<T> = {
  data: T[] | undefined;
  firstOptionText?: string;
  valueKey?: keyof T;
  textKey: keyof T;
};

export const useSelectOptions = <T extends Record<string, any>>({ data, firstOptionText = "Select", valueKey = "id" as keyof T, textKey }: UseSelectOptionsProps<T>): Option[] => {
  const options = useMemo(() => {
    if (!data || data.length === 0) {
      return [{ value: 0, text: firstOptionText }];
    }

    return [
      { value: 0, text: firstOptionText },
      ...data.map((item) => ({
        value: item[valueKey],
        text: item[textKey] as unknown as string,
      })),
    ];
  }, [data, firstOptionText, valueKey, textKey]);

  return options;
};
