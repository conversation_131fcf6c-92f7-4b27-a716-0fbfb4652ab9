// utils/handlers.ts
import { ChangeEvent } from "react";

export const createSelectChangeHandler = (setState: (val: number) => void) => (event: ChangeEvent<HTMLSelectElement>) => {
  const value = parseInt(event.target.value);
  setState(value);
};

export const createDateChangeHandler = (setState: (val: string) => void) => (event: ChangeEvent<HTMLInputElement>) => {
  const value = event.target.value;
  setState(value);
};
