// Import dependencies
import { FC, MouseEvent, ChangeEvent, FocusEvent, Fragment, useState, useEffect } from "react"
import ErrorText from "@components/common/ErrorText"
import classNames from "classnames"

// Define the props type for the TextArea component
type TTextAreaProps = {
    name?: string,
    id?: string,
    value?: string,
    resize?: boolean,
    maxLines?: number,
    placeholder?: string,
    className?: string,
    disabled?: boolean,
    readOnly?: boolean,
    required?: boolean,
    size?: "xs" | "sm" | "md" | "lg",
    variant?: "primary" | "secondary" | "danger" | "default",
    label?: string,
    error?: boolean,
    errorText?: string,
    errorIcon?: boolean,
    onClick?: (event: MouseEvent<HTMLElement>) => void,
    onChange?: (text: ChangeEvent<HTMLTextAreaElement>) => void
    onBlur?: (event: FocusEvent<HTMLTextAreaElement>) => void
    onMouseOver?: (event: MouseEvent<HTMLElement>) => void
}

/**
 * TextArea component props 
 * @params name: The name attribute of the input field
 * @params id: The id attribute of the input field
 * @params value: The current value of the input field 
 * @params disabled: Indicates if the input field is disabled
 * @params readOnly: Indicates if the input field is read-only
 * @params required: Indicates if the input field is required
 * @params placeholder: Placeholder text for the input field
 * @params className: Additional CSS class(es) for styling
 * @params error: Indicates if the input field has an error state
 * @params errorText: Error message text to display when error is true
 * @params errorIcon: Indicates if an error icon should be displayed
 * @params size: Size variant of the input field ("sm", "md", "lg")
 * @params variant: Variant style of the input field ("primary", "secondary", etc.)
 * @params label: Label text for the input field
 * @params onClick: Event handler for click events on the input field
 * @params onChange: Event handler for change events on the input field
 * @params onBlur: Event handler for blur events on the input field
 * @params onMouseOver: Event handler for mouse over events on the input field
 */
const TextArea: FC<TTextAreaProps> = ({
    name,
    id,
    value = "",
    disabled = false,
    readOnly = false,
    required = false,
    resize = true,
    maxLines = 5,
    placeholder = "Type here",
    className = "",
    error = false,
    errorText,
    errorIcon = false,
    size = "md",
    variant = "primary",
    label = "",
    onClick,
    onChange,
    onBlur,
    onMouseOver
}) => {

    const [inputValue, setInputValue] = useState<string>("");

    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        if (onChange) {
            onChange(e)
        }

        setInputValue(e.target.value);
    }

    useEffect(() => {
        setInputValue(value);
    }, [value])

    const textareaClass = classNames(
        "textarea textarea-bordered",
        `border-${variant}`,
        `textarea-${variant}`,
        `textarea-${size}`,
        { "!textarea-error": error },
        { "!resize-none": !resize },
        className
    );

    return (
        <Fragment>
            {label} {/* Render label text if provided */}
            <textarea
                name={name}
                id={id}
                value={inputValue}
                disabled={disabled}
                rows={maxLines}
                placeholder={`${placeholder} ${required ? '*' : ''}`}  // Add asterisk (*) for required fields 
                className={textareaClass}
                onClick={onClick}
                onChange={handleChange}
                onBlur={onBlur}
                onMouseOver={onMouseOver}
                readOnly={readOnly}
                required={required}
            ></textarea>
            {/* Render ErrorText component if error is true */}
            {error &&
                <ErrorText text={errorText} withIcon={errorIcon} />
            }
        </Fragment>
    )
}
// Export TextArea component as default
export default TextArea;
