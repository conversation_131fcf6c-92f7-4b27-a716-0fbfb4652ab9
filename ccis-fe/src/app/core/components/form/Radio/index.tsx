// Import dependencies
import { <PERSON>E<PERSON>, ChangeEvent, FC, useState, useEffect } from 'react';
import ErrorText from '@components/common/ErrorText';
import classNames from 'classnames';

// Define props interface for the Radio component
type TRadioProps = {
    value?: string | number,
    checked?: boolean,
    label?: string,
    labelStyle?: string,
    name?: string,
    id?: string,
    required?: boolean,
    className?: string,
    disabled?: boolean,
    readOnly?: boolean,
    variant?: "primary" | "secondary" | "danger" | "default",
    size?: "xs" | "sm" | "md" | "lg",
    error?: boolean,
    errorText?: string,
    errorIcon?: boolean,
    onClick?: (e: MouseEvent<HTMLInputElement>) => void,
    onChange?: (value: string) => void
};


/**
 * Radio component props.
 * @params value - Value of the radio button.
 * @params checked - Whether the radio button is checked.
 * @params label - Label text for the radio button.
 * @params labelStyle - Additional CSS class for the label.
 * @params name - Name attribute for the radio button.
 * @params id - ID attribute for the radio button.
 * @params required - Whether the radio button is required.
 * @params className - Additional CSS class for the radio button container.
 * @params variant - Variant style of the radio button ("primary", "secondary", "danger", "default").
 * @params size - Size of the radio button ("xs", "sm", "md", "lg").
 * @params error - Whether to display an error state.
 * @params errorText - Error message to display.
 * @params errorIcon - Whether to display an error icon.
 * @params disabled - Whether the radio button is disabled.
 * @params readOnly - Whether the radio button is read-only.
 * @params onClick - Click event handler function.
 * @params onChange - Change event handler function.
 */
const Radio: FC<TRadioProps> = ({
    value,
    checked = false,
    label = "",
    labelStyle = "",
    name,
    id,
    required = false,
    className = "",
    variant = "primary",
    size = "md",
    error = false,
    errorText,
    errorIcon = false,
    disabled = false,
    readOnly = false,
    onClick = () => { },
    onChange = () => { }
}) => {

    const [isPicked, setIsPicked] = useState<boolean>(false);  // State to manage checked state of the radio button

    // Dynamically generate classes based on component state and props
    const labelClass = classNames("label-text pl-2", `text-${variant}`, { '!text-error': error }, labelStyle);
    const radioClass = classNames("radio", `radio-${size}`, `radio-${variant}`, className, { 'radio-error': error });

    // Event handler for radio button change
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);                   // Call user-defined onChange handler
        setIsPicked(!isPicked);        // Toggle the local state to reflect the change
    };

    // Effect to initialize state when checked prop changes
    useEffect(() => {
        // mount
        setIsPicked(checked);           // Set initial state based on checked prop
    }, [checked]);

    // Effect to reset state on unmount
    useEffect(() => {
        // unmount
        return () => {
            setIsPicked(checked);       // Reset state to initial value on unmount
        };
    }, []);

    return (
        <div className="form-control">
            <label className={`label cursor-pointer justify-start radio-${variant}`}>
                <input
                    type="radio"
                    id={id}
                    name={name}
                    readOnly={readOnly}
                    disabled={disabled}
                    value={value ?? label}
                    checked={checked}
                    className={radioClass}
                    onChange={handleChange}
                    onClick={onClick}
                    required={required}
                />
                <span className={labelClass}>{label}</span>
            </label>
            {/* Display error text component if error prop is true */}
            {error &&
                <ErrorText text={errorText} withIcon={errorIcon} />
            }
        </div>
    );
};

export default Radio;  // Export Radio component as default
