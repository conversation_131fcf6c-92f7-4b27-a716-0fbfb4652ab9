import { FC, ReactNode, MouseEvent, ChangeEvent, FocusEvent, Fragment, useState, useEffect } from "react";
import ErrorText from "@components/common/ErrorText";
import classNames from "classnames";

export type TTextFieldProps = {
  type?: string;
  name?: string;
  id?: string;
  value?: string | number;
  min?: number;
  max?: number;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  size?: "xs" | "sm" | "md" | "lg";
  variant?: "primary" | "secondary" | "danger" | "default";
  label?: string;
  error?: boolean;
  errorText?: string;
  errorIcon?: boolean;
  autoComplete?: string;
  validationPattern?: RegExp; // New prop for validation
  onClick?: (event: MouseEvent<HTMLElement>) => void;
  onChange?: (text: ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: FocusEvent<HTMLInputElement>) => void;
  onMouseOver?: (event: MouseEvent<HTMLElement>) => void;
  onFocus?: (event: FocusEvent<HTMLInputElement>) => void;
};

const TextField: FC<TTextFieldProps> = ({
  type = "text",
  name,
  id,
  value = "",
  max,
  min,
  leftIcon,
  rightIcon,
  disabled = false,
  readOnly = false,
  required = false,
  placeholder = "Type here",
  className = "",
  error = false,
  errorText,
  errorIcon = false,
  size = "md",
  variant = "primary",
  label = "",
  autoComplete = "off",
  validationPattern, // Destructure the new prop
  onClick,
  onChange,
  onBlur,
  onMouseOver,
  onFocus,
}) => {
  const [inputValue, setInputValue] = useState<string>("");

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Validate input based on the validationPattern
    if (!validationPattern || validationPattern.test(newValue)) {
      setInputValue(newValue);
      if (onChange) {
        onChange(e);
      }
    }
  };

  const handleFocus = (e: FocusEvent<HTMLInputElement>) => {
    if (onFocus) {
      onFocus(e);
    }
  };

  useEffect(() => {
    setInputValue(value?.toString() ?? "");
  }, [value]);

  const inputWrapperClass = classNames("flex input input-bordered items-center gap-2 w-full", `input-${size}`, `border-${variant}`, { "!input-error": error }, `input-${variant}`, className);
  const inputClass = classNames("grow border-0 px-0 input !border-transparent focus-within:outline-none", `input-${variant}`, `input-${size}`, rightIcon ? "pr-10 lg:pr-0" : "");

  return (
    <Fragment>
      <label className={inputWrapperClass}>
        {leftIcon && <span className="w-auto h-auto">{leftIcon}</span>}
        {label}
        <input
          type={type}
          name={name}
          id={id}
          value={inputValue}
          disabled={disabled}
          placeholder={`${placeholder} ${required ? "*" : ""}`}
          className={inputClass}
          onClick={onClick}
          onChange={handleChange}
          onBlur={onBlur}
          onMouseOver={onMouseOver}
          readOnly={readOnly}
          required={required}
          max={type === "number" ? max : undefined}
          min={type === "number" ? min : undefined}
          onFocus={handleFocus}
          autoComplete={autoComplete}
        />
        {rightIcon && <span className="w-auto h-auto">{rightIcon}</span>}
      </label>
      {error && <ErrorText text={errorText} withIcon={errorIcon} />}
    </Fragment>
  );
};

export default TextField;
