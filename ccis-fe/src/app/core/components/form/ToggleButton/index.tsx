import classNames from "classnames";
import { FC } from "react";

type Props = {
    value: boolean;
    toggle: (bool: boolean) => void;
    variant?: "success" | "warning" | "info" | "error",
    size?: "xs" | "sm" | "md" | "lg";
    leftLabel?: string,
    rightLabel?: string,
    labelClass?: string,
}

const ToggleButton: FC<Props> = ({
    value = false,
    toggle,
    variant = 'success',
    size = 'md',
    leftLabel,
    rightLabel,
    labelClass
}) => {

    const handleToggle = () => {
        toggle && toggle(!value)
    }

    const toggleWrapper = classNames('toggle', `toggle-${variant}`, `toggle-${size}`)
    const labelClasses = classNames(labelClass)
    return (
        <div className="form-control">
            <div className="flex flex-row">
                {
                    leftLabel && <span className={`pr-2 ${labelClasses}`}>{leftLabel}</span> // Render the left label if provided
                }
                <input onClick={handleToggle} type="checkbox" className={toggleWrapper} defaultChecked={value} />
                {
                    rightLabel && <span className={`pl-2 ${labelClass}`}>{rightLabel}</span> // Render the right label if provided
                }
            </div>
        </div>

    )
}

export default ToggleButton;