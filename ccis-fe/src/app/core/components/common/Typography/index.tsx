import classNames from "classnames";
import { FC, ReactNode } from "react";

type TTypographyProps = {
  children?: ReactNode | ReactNode[];
  text?: string;
  size?:
    | "base"
    | "xs"
    | "sm"
    | "md"
    | "lg"
    | "xl"
    | "2xl"
    | "3xl"
    | "4xl"
    | "5xl";
  variant?: "primary" | "secondary" | "accent" | "error" | "success" | "black";
  className?: string;
};

const Typography: FC<TTypographyProps> = ({
  text,
  size = "base",
  variant = "black",
  children,
  className,
}) => {
  const classes = classNames(`text-${size}`, `text-${variant}`, className);

  return (
    <div className={classes}>
      {children && children}
      {!children && text}
    </div>
  );
};

export default Typography;
