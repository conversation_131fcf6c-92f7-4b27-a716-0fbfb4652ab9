// Import dependencies
import { ChangeEvent, FC, Fragment } from "react";
import Datatable, { TableColumn } from "react-data-table-component";
import TextField from "@components/form/TextField";
import { FaUserPlus, FaSearch } from "react-icons/fa";
import Loader from "@components/Loader";
import classNames from "classnames";
import Button from "../Button";
import { useDebouncedCallback } from "use-debounce";

// Define the type for the Table component props
type TTableProps = {
  columns: TableColumn<any>[];
  data?: any[];
  loading?: boolean;
  searchable?: boolean;
  sortable?: boolean;
  selectable?: boolean;
  multiSelect?: boolean;
  fixHeader?: boolean;
  pagination?: boolean;
  createLabel?: string;
  className?: string;
  disabled?: boolean;
  onCreate?: () => void;
  onSearch?: (e: string) => void;
  onPaginate?: (data: any) => void;
  onChangeRowsPerPage?: (
    currentRowsPerPage: number,
    currentPage: number
  ) => void;
  paginationTotalRows?: number;
  paginationServer?: boolean;
  defaultSortFieldId?: string;
  defaultSortAsc?: boolean;
  hideButton?: "invisible" | "visible";
};

/**
 * @param columns - Columns configuration for the table (default: empty array)
 * @param data - Data to populate the table (default: empty array)
 * @param createLabel - Label for the create button (default: "Add")
 * @param className - Additional class names for the table container
 * @param onCreate - Callback function for create button click
 * @param onSearch - Callback function for search action
 * @param loading - Flag to indicate if table is in loading state (default: false)
 * @param searchable - Flag to enable/disable search functionality (default: true)
 * @param selectable - Flag to enable/disable row selection (default: true)
 * @param multiSelect - Flag to enable/disable multiple row selection (default: false)
 * @param pagination - Flag to enable/disable pagination (default: true)
 * @param fixHeader - Flag to enable/disable fix header (default: false)
 *  @param hideButton - Flag to invisible/visible the button (default: visible)
 */
const Table: FC<TTableProps> = ({
  columns = [],
  data = [],
  createLabel = "Add",
  className,
  onCreate,
  onSearch,
  onPaginate,
  loading = false,
  searchable = false,
  selectable = true,
  multiSelect = false,
  pagination = true,
  fixHeader = true,
  paginationServer = false,
  paginationTotalRows,
  onChangeRowsPerPage,
  defaultSortFieldId,
  defaultSortAsc,
  disabled,
  hideButton = "visible",
}) => {
  // Combine class names for the table container
  const tableClass = classNames("!overflow-y-scroll", className);

  /**
   * handleSearch - Handles the search input with debouncing
   *
   * @param event - The input change event
   */
  const handleSearch = useDebouncedCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      // Extract the current input value from the event
      const value = event.target.value;

      // Check if the onSearch callback is provided
      if (onSearch) {
        // Call the onSearch callback with the input value
        onSearch(value);
      }
    },
    500
  ); // 500ms debounce delay

  return (
    <Fragment>
      <div className="flex flex-1 flex-col w-full">
        {/* Flag to check if search options is available */}
        {searchable && (
          <Fragment>
            {/* Render search input field and create button if searchable */}
            <div className="flex flex-1 flex-row justify-between mb-4">
              <div className="w-[35%] flex justify-start">
                <TextField
                  rightIcon={<FaSearch className="text-accent" />}
                  placeholder="Search"
                  size="xs"
                  className="input-sm"
                  variant="primary"
                  onChange={handleSearch}
                />
              </div>
              <div className="w-[25%] flex justify-end">
                <Button
                  classNames={`flex flex-row btn-sm items-center ${hideButton} ${disabled ? `cursor-not-allowed opacity-50` : ``
                    }`}
                  // classNames={`flex flex-row btn-sm items-center` $disabled ? 'cursor-not-allowed' : ``}
                  // "flex flex-row btn-sm items-center"
                  variant="primary"
                  isSubmitting={disabled}
                  onClick={onCreate}
                >
                  {createLabel}
                  <FaUserPlus className="pl-2" size={20} />
                </Button>
              </div>
            </div>
          </Fragment>
        )}
        {/* Render DataTable component with specified props */}
        <Datatable
          fixedHeader={fixHeader}
          columns={columns}
          data={data ?? []}
          selectableRows={selectable}
          selectableRowsSingle={multiSelect}
          pagination={pagination}
          className={tableClass}
          progressComponent={<Loader />}
          progressPending={loading}
          onChangePage={onPaginate}
          onChangeRowsPerPage={onChangeRowsPerPage}
          paginationServer={paginationServer}
          paginationTotalRows={paginationTotalRows}
          defaultSortFieldId={defaultSortFieldId}
          defaultSortAsc={defaultSortAsc}
        />
      </div>
    </Fragment>
  );
};

// Export the Table component as the default export
export default Table;
