import classNames from "classnames";
import { FC, Fragment, ReactNode } from "react";
import { imageStorageUrl } from "@services/variables";

type TAvatarProps = {
  src?: string;
  children?: ReactNode | ReactNode[];
  classNames?: string;
  alt?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  imageStyle?: string;
  placeholderStyle?: string;
  shape?: "square" | "circle";
};

const Avatar: FC<TAvatarProps> = ({ src = "", children, alt = "User avatar", size = "xs", imageStyle, placeholderStyle, shape = "circle" }) => {
  const sizes = classNames({ "size-10": size === "xs" }, { "size-40": size === "sm" }, { "size-52": size === "md" }, { "size-72": size === "lg" }, { "size-80": size === "xl" });

  const wrapperClass = classNames(
    "btn btn-ghost z-10",
    { "rounded-full btn-circle": shape === "circle" },
    { "size-12": size === "xs" },
    { "size-44": size === "sm" },
    { "size-56": size === "md" },
    { "size-78": size === "lg" },
    { "size-96": size === "xl" },
    classNames
  );
  const imageClass = classNames("avatar", { "rounded-full": shape === "circle" }, sizes, imageStyle);

  const placeholderClass = classNames("bg-slate-400 text-center flex items-center justify-center ", { "rounded-full": shape === "circle" }, sizes, placeholderStyle);
  
  return (
    <Fragment>
      <div role="button" className={wrapperClass}>
        {src !== "" && <img alt={alt} src={`${imageStorageUrl}/${src}`} className={imageClass} />}
        {/* Fallback display if no image is set */}
        {src === "" && <div className={placeholderClass}>{children && children}</div>}
      </div>
    </Fragment>
  );
};

export default Avatar;
