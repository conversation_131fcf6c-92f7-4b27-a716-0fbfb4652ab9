import { useState, Fragment, FC, useEffect } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

type TWysiwygProps = {
  stateValue?: string;
  onChange?: (content: string) => void;
  guidelineIndex?: number;
  contentIndex?: number;
  className?: string;
  classNames?: string;
};

const Wysiwyg: FC<TWysiwygProps> = ({ onChange, stateValue, className }) => {
  const classnames = [className, "flex flex-1 flex-col"].filter(Boolean).join(" ");
  const [value, setValue] = useState<string>(stateValue || "");

  const handleChange = (text: string) => {
    setValue(text);
    onChange && onChange(text);
  };

  useEffect(() => {
    setValue(() => stateValue || "");
  }, [stateValue]);

  return (
    <Fragment>
      <div className={classnames}>
        <ReactQuill
          theme="snow"
          value={value}
          defaultValue={value}
          onChange={handleChange}
          modules={{
            toolbar: [
              [{ header: [] }, { font: [] }],
              [{ size: [] }],
              ["bold", "italic", "underline", "strike", "blockquote"],
              [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
              ["clean"],
              [{ color: [] }, { background: [] }],
              [{ align: "" }, { align: "center" }, { align: "right" }, { align: "justify" }],
              ["link", "image", "video"],
              // [],
            ],
          }}
        />
      </div>
    </Fragment>
  );
};

export default Wysiwyg;
