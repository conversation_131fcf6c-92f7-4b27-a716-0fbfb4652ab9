import React, { forwardRef } from "react";
import classNames from "classnames";

type Props = {
  type?: "button" | "submit" | "reset";
  onClick?: () => void;
  onMouseEnter?: () => void;
  children?: React.ReactNode;
  variant?: "primary" | "secondary" | "danger" | "default" | "gradient" | "success" | "custom";
  block?: boolean;
  outline?: boolean;
  classNames?: string;
  isSubmitting?: boolean;
  disabled?: boolean;
};

const Button = forwardRef<HTMLButtonElement, Props>(
  ({ type = "button", onClick, onMouseEnter, children, variant = "default", block = false, outline = false, classNames: additionalClasses, isSubmitting = false }, ref) => {
    const baseClasses = classNames("px-4 py-2 rounded");

    const variantClasses = {
      primary: outline ? "border border-primary text-primary hover:bg-primary hover:text-white text-primary" : "bg-primary hover:bg-primary-dark text-white",
      secondary: outline ? "border border-secondary text-secondary hover:bg-secondary hover:text-white text-primary" : "bg-secondary hover:bg-secondary-dark text-white",
      danger: outline ? "border border-danger text-danger hover:bg-danger hover:text-white text-primary" : "bg-danger hover:bg-danger-dark text-white",
      default: outline ? "border border-gray-300 text-gray-700 hover:bg-gray-300 hover:text-gray-900 text-primary" : "bg-gray-300 text-gray-700 hover:bg-gray-400 text-white",
      gradient: "bg-gradient-primary hover:bg-gradient-primary-hover text-white",
      success: outline ? "border border-success text-success hover:bg-success hover:text-white text-primary" : "bg-success hover:bg-success-dark text-white",
      custom: "mt-4 border border-[#8B909F] text-[#8B909F] hover:bg-[#8B909F] hover:text-white w-full text-xs",
    };

    const blockClasses = block ? "w-full" : "";

    const classes = classNames(baseClasses, variantClasses[variant], blockClasses, additionalClasses);

    return (
      <button disabled={isSubmitting} type={type} onClick={onClick} className={classes} onMouseEnter={onMouseEnter} ref={ref}>
        {children}
      </button>
    );
  }
);

export default Button;
