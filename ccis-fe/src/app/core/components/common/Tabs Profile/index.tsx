// Import necessary modules and types from React and classNames library
import { FC, Fragment, ReactNode, useState } from "react";
import classNames from "classnames";

// Define the type for the Tabs component props
type TTabsProps = {
    headers: string[] | ReactNode[],
    contents: ReactNode[],
    size?: "xs" | "sm" | "md" | "lg",
    type?: "bordered" | "lifted" | "boxed",
    className?: string,
    headerClass?: string,
    contentClass?: string,
}


// Define the Tabs component using the functional component syntax
/**
 * @param type - Optional type for the tab styling (default: "bordered")
 * @param headers - Array of header labels for the tabs
 * @param contents - Array of content for each tab
 * @param size - Optional size with options ("xs" | "sm" | "md" | "lg") for the tabs (default: "lg")
 */
const Tabs: FC<TTabsProps> = ({
    type = "bordered",
    headers,
    contents,
    size = "lg",
    className,
    headerClass,
    contentClass,
}) => {

    // Check if headers and contents arrays are of the same length and warn if not
    if (headers?.length !== contents?.length) {
        console.warn("Headers and contents should be equal in length")
    }

    // State to keep track of the active tab index, defaulting to the first tab
    const [activeTab, setActiveTab] = useState<number | string>(0)

    // Handler to update the active tab index
    const handleActiveTab = (index: number) => {
        setActiveTab(index)
    }

    // Combine class names for the tabs based on the type and size props
    const classes = classNames("tabs", `tabs-${type}`, `tabs-${size}`, className)
    const headerClasses = classNames(headerClass);
    const contentClasses = classNames('tab-content bg-base-100 border-base-300 p-6', contentClass);

    return (
        <Fragment>
            <div role="tablist" className={classes}>
                {/* Iterate all the headers */}
                {headers.map((value, index) => {
                    // Determine if the current tab is active to apply the active class
                    const activeClass = index === activeTab ? "tab-active" : ''

                    return (
                        <Fragment key={`tab-${value}-${index}`}>
                            {/* Render the tab header as a anchor tag */}
                            <a role="tab" className={`tab ${headerClasses} ${activeClass}`} onClick={() => handleActiveTab(index)}>
                                {value}
                            </a>
                            {/* Render the content for the tab if it is active */}
                            {activeTab === index &&
                                <div role="tabpanel" className={`${contentClasses}`}>
                                    {contents[index]}
                                </div>
                            }
                        </Fragment>
                    )
                })}
            </div>
        </Fragment>
    )
}

// Export the Tabs component as the default export
export default Tabs
