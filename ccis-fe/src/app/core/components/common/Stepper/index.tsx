import classNames from 'classnames';
import { FC, Fragment, ReactNode } from 'react'

type TStep = {
    label: string;
    icon: ReactNode;
}

type TStepperProps = {
    currentStep: number;
    setStep?: (step: number) => void;
    steps: TStep[];
    variant?: "primary" | "secondary" | "accent" | "success" | "warning" | "error",
    strictMode: boolean;
}

const Stepper: FC<TStepperProps> = ({
    currentStep,
    setStep,
    steps,
    variant = "primary",
    strictMode = false
}) => {

    const nodeClass = classNames('w-10 h-10 rounded-full flex items-center justify-center');
    const lineClass = classNames('flex-grow border-2 transition duration-500 ease-in-out -mt-10 mx-[10px]', {});

    const handleStep = (step: number) => {
        if (setStep && !strictMode) {
            setStep(step)
        }
    }

    return (
        <div className="flex flex-1 flex-row items-center p-2">
            {steps.map((step, index) => (
                <Fragment key={`stepper-step-${index}`}>
                    <div className="flex flex-col items-center w-5 hover:cursor-pointer" onClick={() => handleStep(index)}>
                        <div
                            className={`${currentStep >= index ? `bg-${variant} text-white` : 'bg-slate-300 text-gray-500'} ${nodeClass}`}
                        >
                            {step?.icon}
                        </div>
                        <div className="mt-2 text-center text-sm min-h-10 text-nowrap">
                            <span>{step.label}</span>
                        </div>
                    </div>
                    {index !== steps.length - 1 && (
                        <div className={`${currentStep > index ? `border-${variant}` : "border-slate-300"} ${lineClass}`}></div>
                    )}
                </Fragment>

            ))}
        </div>
    )
}

export default Stepper;