import dayjs from "dayjs";
import { FC } from "react";
import Typography from "../Typography";
import { FaCircleCheck } from "react-icons/fa6";
import classNames from "classnames";
import { ITimelineItemProps } from "@interface/product-proposal.interface";


type TTimelineProps = {
  data?: ITimelineItemProps[];
  fullHeight?: boolean;
};

const Timeline: FC<TTimelineProps> = ({ data = [], fullHeight = false }) => {

  const className = classNames("timeline timeline-compact timeline-vertical !items-start w-full", { "h-full": fullHeight });

  return (
    <ul className={className}>
      {
        data?.map((item, index) => {
          const title = item.title ?? '';
          return <li key={`timeline-item-${index}`} className="w-full m-0">
            <hr />
            <div className="timeline-middle">
              {item.icon ? item.icon : <FaCircleCheck className="text-success" />}
            </div>
            <div className="timeline-end timeline-box w-full items-center">
              {item.component ? item.component : (
                <div className="flex flex-col flex-1">
                  <Typography size="sm">{title.charAt(0).toUpperCase() + title.slice(1)}</Typography>
                  <Typography size="xs">
                    {dayjs(item.date).format("DD MMMM YYYY - h:m A")}
                  </Typography>
                </div>
              )}
            </div>
            <hr />
          </li>
        })
      }
    </ul>
  );
};

export default Timeline;
