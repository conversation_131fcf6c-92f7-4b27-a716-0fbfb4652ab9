import { Menu } from "lucide-react";
import { FC, useContext, useEffect, useRef } from "react";
import { DashboardContext } from "@context/dashboardContext";
import Dropdown from "../DropdownMenu";
import Avatar from "@components/common/Avatar";
import Notification from "@components/common/Notification";
import Message from "@components/common/Message";
import TextField from "@components/form/TextField";
import { LuSearch } from "react-icons/lu";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import useEcho from "@hooks/useEcho";
import { useNotificationActions } from "@state/reducer/notification";
// import { useNotificationActions } from "@state/reducer/notification";
import sound from "@assets/alert-2.mp3";

const NavBar: FC = () => {
  const user = useSelector((state: RootState) => state?.auth?.login?.data?.user);
  const notifications = useSelector((state: RootState) => state?.notification?.notifications);
  const { getNotifications } = useNotificationActions();
  const profile = useSelector((state: RootState) => state?.profile?.profile);
  const profilePicPath = profile?.profilePicturePath;
  const dashboardContext = useContext(DashboardContext);
  const handleMenuCollapse = () => {
    dashboardContext?.handleCollapse();
  };

  // Notification
  const echo = useEcho();
  const { addNotification } = useNotificationActions();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const initAudio = () => {
    if (!audioRef.current) {
      audioRef.current = new Audio(sound);
      audioRef.current.load(); // Preload
    }
  };

  const playAudio = () => {
    if (audioRef.current) {
      audioRef.current.play().catch((err) => {
        console.warn("Play failed:", err);
      });
    }
  };

  useEffect(() => {
    if (!echo || !user?.id) return;

    const channelName = `notification.${user.id}`;
    const channel = echo.private(channelName);

    channel.listen(".notification.log", (event: any) => {
      addNotification(event.data);
      console.log("Notification received:", event);
      playAudio();
    });

    echo.connector.pusher.connection.bind("connected", () => {
      console.log("Connected to WebSocket");
    });

    return () => {
      echo.leave(channelName);
    };
  }, [echo, user?.id]);

  // App.tsx or main layout
  useEffect(() => {
    const unlockAudio = () => {
      initAudio();
      document.removeEventListener("click", unlockAudio);
    };

    document.addEventListener("click", unlockAudio);
    return () => document.removeEventListener("click", unlockAudio);
  }, []);

  useEffect(() => {
    getNotifications();
  }, []);

  return (
    <div className="navbar bg-base-100 xl:w-full w-screen drop-shadow-sm">
      <div className="flex-1">
        <div tabIndex={0} role="button" onClick={handleMenuCollapse} className="btn btn-ghost btn-circle mr-2">
          <div className="indicator">
            <Menu />
          </div>
        </div>
        <div className="form-control w-[200px]  hidden md:w-[300px] sm:block xl:w-[450px]  ">
          <TextField rightIcon={<LuSearch className="text-accent" />} variant="primary" size="sm" />
        </div>
      </div>
      <div className="flex-none">
        <div className="flex justify-start items-start">
          <Message />
          <Notification count={notifications.filter((n: any) => n.readAt === null).length} data={notifications} />
          <Avatar size="xs" src={profilePicPath ?? ""}>
            {profile?.firstname[0]}
            {profile?.lastname[0]}
          </Avatar>
        </div>
        <Dropdown />
      </div>
    </div>
  );
};

export default NavBar;
