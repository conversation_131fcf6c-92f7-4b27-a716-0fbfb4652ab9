import { ReactNode, useContext, FC } from "react";
import { DashboardContext } from "../../../context/dashboardContext";
import { RouteItem } from "src/app/core/interface/routes.interface";
import NavItem from "../NavItem";
import classNames from "classnames";
import { FaChevronRight } from "react-icons/fa";
import { isMobile } from "react-device-detect";

type TNavItemProps = {
  index?: number;
  icon?: ReactNode;
  title: string;
  children?: RouteItem[];
};

const NavItemCollapse: FC<TNavItemProps> = ({ index, icon, title, children }) => {

  const dashboardContext = useContext(DashboardContext);
  const navCollapseItemClass = classNames("collapse text-white -mt-1", { " collapse-arrow ": !dashboardContext?.collapse });
  const navCollapseItemIcon = classNames("pr-2 ml-2", { "ml-2": dashboardContext?.collapse });

  return (
    <div tabIndex={index} className={navCollapseItemClass}>
      <input type="checkbox" onClick={() => {
        if (!isMobile && dashboardContext?.collapse === true) {
          dashboardContext?.handleCollapse(false)
        }
      }} />
      <div className="collapse-title flex flex-row text-sm">
        <div className={navCollapseItemIcon}>{icon ?? ""}</div>
        {!dashboardContext?.collapse && <div>{title}</div>}
        {dashboardContext?.collapse && <div className="mt-1"><FaChevronRight size={10} /></div>}
      </div>
      {!dashboardContext?.collapse &&
        <div className='collapse-content p-0'>
          <div className="flex flex-col flex-1">
            {children?.map((value, index) => {
              const icon = value.icon ? < value.icon size={20} /> : <></>
              return <NavItem key={`collapse-menu-${index}`} index={index} path={value.path} title={value.name} icon={icon} />;
            })}
          </div>
        </div>
      }
      {/* {
        dashboardContext?.collapse &&
        <div className="collapse-content fixed left-20 -mt-12 z-50 bg-slate-100 min-w-52 text-primary !p-0 mb-0 drop-shadow-md">
          <div className="flex flex-col flex-1">
            {children?.map((value, index) => {
              const icon = value.icon ? < value.icon size={20} /> : <></>
              return <NavLink key={`collapse2-menu-${index}`} to={value.path} tabIndex={index} className={({ isActive }) => {
                return isActive ? `${activeNavItemClass}` : "";
              }}>
                <div className="flex flex-row text-sm pl-4 p-2 hover:bg-slate-300">
                  <div className="flex flex-row">{icon}{value.name}</div>
                </div>
              </NavLink>;
            })}
          </div>
        </div>
      } */}

    </div >
  );
};

export default NavItemCollapse;
