import { ReactNode, useContext, FC } from "react";
import { NavLink } from "react-router-dom";
import { DashboardContext } from "../../../context/dashboardContext";
import classNames from "classnames";
import { isMobile } from "react-device-detect";

type TNavItemProps = {
  index?: number;
  icon?: ReactNode;
  title: string;
  path: string;
};

const NavItem: FC<TNavItemProps> = ({ index, icon, title, path }) => {
  const dashboardContext = useContext(DashboardContext);

  const navItemWrapperClass = classNames("cursor-pointer border-l-[10px] border-transparent text-white hover:bg-slate-400 w-full hover:border-l-[10px] hover:border-yellow-300 p-3", {
    "xl:block": dashboardContext?.collapse,
  });

  const activeNavItemClass = classNames("bg-slate-400 cursor-pointer border-l-[10px] !border-yellow-300");

  const collapseNavItemClass = classNames("pr-2", { "!pl-": dashboardContext?.collapse });

  const handleCollapse = () => {
    if (!isMobile) {
      return;
    }
    dashboardContext?.handleCollapse();
  }

  return (
    <NavLink
      to={path}
      tabIndex={index}
      className={({ isActive }) => {
        return isActive ? `${navItemWrapperClass} ${activeNavItemClass}` : navItemWrapperClass;
      }}
      onClick={handleCollapse}
    >
      <div className="flex flex-row text-sm">
        <div className={collapseNavItemClass}>{icon}</div>
        {!dashboardContext?.collapse && <div>{title}</div>}
      </div>
    </NavLink>
  );
};

export default NavItem;
