// src/components/Dropdown.js
import { ROUTES } from "@constants/routes";
import { UserRoles } from "@interface/routes.interface";
import { logoutRequest } from "@services/auth/auth.service";
import { handleLogout } from "@services/utils/utils.service";
import { RootState } from "@state/store";
import { FC, useState } from "react";
import { FaSignOutAlt, FaUser } from "react-icons/fa";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";

const Dropdown: FC = () => {
  const user = useSelector((state: RootState) => state.auth.user.data);
  const [isOpen, setIsOpen] = useState(false); // Set initial state to true to make the dropdown open initially

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleAccountLogout = async () => {
    try {
      const { status } = await logoutRequest();
      //@ts-expect-error status is not a number
      if (status === "success") {
        handleLogout();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const { data: currentUser } = useSelector((state: RootState) => state.auth.user);

  return (
    <div className="dropdown relative inline-block !bg-base-100 p-0">
      <label tabIndex={0} className="flex items-center justify-center xl:text-base text-xs border-none text-black hover:bg-transparent hover:border-none" onClick={toggleDropdown}>
        {user?.firstname}
        <svg className="fill-current ml-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
          <path d="M7 10l5 5 5-5z" />
        </svg>
      </label>
      {isOpen && (
        <div className="absolute right-0 mt-2 w-52 max-w-xs z-50" onMouseLeave={() => setIsOpen(false)}>
          <div className="relative">
            {/* Arrow pointing up to the trigger */}
            <div className="absolute -top-1 right-6 w-2 h-2 bg-base-100 border-l border-t border-zinc-300 transform rotate-45 z-30"></div>
            <ul tabIndex={0} className="dropdown-content menu p-2 pl-0 w-full bg-base-100 relative z-20 shadow-xl border border-zinc-300">
              <li>
                <Link to={currentUser?.roles?.some((role: any) => role.name.toLocaleLowerCase() === UserRoles.admin) ? ROUTES.ADMIN.profile.key : ROUTES.USERS.profile.key}>
                  <FaUser />
                  Profile
                </Link>
              </li>
              <li>
                <Link to={""} onClick={handleAccountLogout}>
                  <FaSignOutAlt />
                  Logout
                </Link>
              </li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
