import { FC } from "react";
import NavItem from "../NavItem";
import NavItemCollapse from "../NavCollapseItem";
import { RouteItem } from "src/app/core/interface/routes.interface";

type TNavProps = {
    list: RouteItem[];
};

const Nav: FC<TNavProps> = ({ list }) => {
    return (
        <div className="flex flex-col bg-primary justify-start pt-2">
            {list.map((value, index) => {
                const icon = value.icon ? <value.icon size={20} /> : <></>
                if (value.isSidebar) {
                    if (value.children) {
                        return <div
                            className="mt-1 flex flex-1"
                            key={`nav-item${index}`}>
                            <NavItemCollapse title={value.name} icon={icon} children={value.children} index={index} />
                        </div>
                    } else {
                        return <div
                            className="mt-1 flex flex-1 mb-2"
                            key={`nav-item${index}`}>
                            <NavItem index={index} path={value.path} title={value.name} icon={icon} />
                        </div>
                    }
                }
            })}
        </div>
    )
}

export default Nav;
