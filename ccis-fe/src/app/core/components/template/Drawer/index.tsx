import { FC, ReactNode } from 'react'
// import component 👇
import Drawer from 'react-modern-drawer'
//import styles 👇
import 'react-modern-drawer/dist/index.css'

type TDrawerProps = {
    direction: 'left' | 'right';
    isOpen?: boolean;
    handleDrawer?: () => void;
    children: ReactNode | ReactNode[],
    className?: string;
}

const SideDrawer: FC<TDrawerProps> = ({ direction = 'right', isOpen = false, handleDrawer, children, className }) => {

    return (
        <Drawer
            open={isOpen}
            onClose={handleDrawer}
            direction={direction}
            className={`!w-96 ${className}`}
        >
            <div className='h-dvh overflow-y-auto'>
                {children}
            </div>
        </Drawer>
    )
}

export default SideDrawer;