import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { getGamPadRequestService, getLastSeriesNumberService, getRemainingPadsService, postGamPadRequestService } from "@services/gam-pad-request/gam-pad-request.service";
import { handleServerException } from "@services/utils/utils.service";
import {
  getPadRequests,
  getPadRequestsSuccess,
  getPadRequestsFailure,
  getRemainingPads,
  getRemainingPadsFailure,
  getRemainingPadsSuccess,
  getLastSeries,
  getLastSeriesSuccess,
  getLastSeriesFailure,
  postGamPadRequest,
  postGamPadRequestFailure,
  postGamPadRequestSuccess,
} from "@state/reducer/gam-pad-request";
import { TGamPadRequestActionPayloadAction } from "@state/types/gam-pad-request";
import { AxiosResponse } from "axios";
import { call, put, takeLatest } from "redux-saga/effects";

function* getGamPadRequestSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getGamPadRequestService, payload.params);
    yield put(getPadRequestsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getPadRequestsFailure.type, true);
  }
}

function* getRemainingPadsSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getRemainingPadsService, { filter: payload.params.filter });
    yield put(getRemainingPadsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getRemainingPadsFailure.type, true);
  }
}

function* getLastSeriesSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getLastSeriesNumberService, { filter: payload.params.filter });
    yield put(getLastSeriesSuccess(result.data[0]));
  } catch (error) {
    yield call(handleServerException, error, getLastSeriesFailure.type, true);
  }
}

function* postGamPadRequestSaga(actions: TGamPadRequestActionPayloadAction) {
  try {
    const { data }: AxiosResponse = yield call(postGamPadRequestService, actions.payload); // Call the postProducts service with the action payload and destructure data from the response
    yield put(postGamPadRequestSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, postGamPadRequestFailure.type, true); // Handle any errors using handleServerException utility
  }
}

export function* GamRequestSaga() {
  yield takeLatest(getPadRequests.type, getGamPadRequestSaga);
  yield takeLatest(getRemainingPads.type, getRemainingPadsSaga);
  yield takeLatest(getLastSeries.type, getLastSeriesSaga);
  yield takeLatest(postGamPadRequest.type, postGamPadRequestSaga);
}
