import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "redux";
import { useDispatch } from "react-redux";

import { TGamPadRequestActionPayloadAction, TGamPadRequestState, TGamPadRequestWithIndex } from "@state/types/gam-pad-request";
import { IDefaultParams } from "@interface/common.interface";

const initialState: TGamPadRequestState = {
  remainingPads: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  lastSeries: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  postGamPadRequest: {
    loading: false,
    success: false,
    error: false,
  },
  gamPadRequesTable: {
    selectedRecord: {} as TGamPadRequestWithIndex,
    fetchResult: {
      data: undefined,
      loading: false,
      success: false,
      error: false,
    },
  },
};

const padRequestSlice = createSlice({
  name: "padRequest",
  initialState,
  reducers: {
    getPadRequests(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.gamPadRequesTable.fetchResult = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getPadRequestsSuccess(state, action) {
      state.gamPadRequesTable.fetchResult = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getPadRequestsFailure(state) {
      state.gamPadRequesTable.fetchResult = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getRemainingPads(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.remainingPads = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getRemainingPadsSuccess(state, action) {
      state.remainingPads = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getRemainingPadsFailure(state) {
      state.remainingPads = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getLastSeries(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.lastSeries = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getLastSeriesSuccess(state, action) {
      state.lastSeries = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getLastSeriesFailure(state) {
      state.lastSeries = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    postGamPadRequest(state, _action: TGamPadRequestActionPayloadAction) {
      state.postGamPadRequest = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postGamPadRequestSuccess(state) {
      state.postGamPadRequest = {
        loading: false,
        success: true,
        error: false,
      };
    },
    postGamPadRequestFailure(state) {
      state.postGamPadRequest = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    postGamPadRequestStateDefault(state) {
      state.postGamPadRequest = {
        loading: false,
        success: false,
        error: false,
      };
    },
  },
});

export const {
  getPadRequests,
  getPadRequestsSuccess,
  getPadRequestsFailure,
  getRemainingPads,
  getRemainingPadsFailure,
  getRemainingPadsSuccess,
  getLastSeries,
  getLastSeriesSuccess,
  getLastSeriesFailure,
  postGamPadRequest,
  postGamPadRequestFailure,
  postGamPadRequestSuccess,
  postGamPadRequestStateDefault,
} = padRequestSlice.actions;

export const usePadRequestActions = () => {
  return bindActionCreators(
    {
      getPadRequests,
      getPadRequestsSuccess,
      getPadRequestsFailure,
      getRemainingPads,
      getRemainingPadsFailure,
      getRemainingPadsSuccess,
      getLastSeries,
      getLastSeriesSuccess,
      getLastSeriesFailure,
      postGamPadRequest,
      postGamPadRequestFailure,
      postGamPadRequestSuccess,
      postGamPadRequestStateDefault,
    },
    useDispatch()
  );
};

export default padRequestSlice.reducer;
