// Import dependencies
import { createDynamicState } from "@helpers/array";
import { showSuccess } from "@helpers/prompt";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";

import {
    TIGlobalSettingsActionPayloadPostSuccess,
    TIGlobalSettingsActionPayloadSelectedPutSuccess,
    TGlobalSettingsActionPayloadPostPut,
    TGlobalSettingsDelete,
    TGlobalSettingsManagementState,
} from "@state/types/global-settings";

import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

const initialState: TGlobalSettingsManagementState = {
    globalSettings: [],
    
    selectedGlobalSettings: {
        index: 0,
        data: {
            key: "",
            value: "",
        },
    },
    getGlobalSettings: createDynamicState(),
    postGlobalSettings: createDynamicState(),
    putGlobalSettings: createDynamicState(),
    destroyGlobalSettings: createDynamicState(),
};