import { IDivision, IFormType } from "./form-inventory-utilities";
import { IUser } from "./user.interface";
import { IUserArea } from "./utilities.interface";

export interface IGamPadRequest {
  id?: number;
  divisionId?: number;
  division?: IDivision;
  formTypeId?: number;
  formType?: IFormType;
  areaId?: number;
  area?: IUserArea;
  numberOfPads?: number;
  seriesFrom?: number;
  seriesTo?: number;
  releasedTo?: IUser;
  status?: string | undefined;
  createdAt?: Date;
  releasedUser?: IUser;
  createdBy?: IUser;
}

export interface ICreatePadRequest {
  divisionId: number;
  formTypeId: number;
  areaId: number;
  numberOfPads: number;
  seriesFrom: number;
  seriesTo: number;
  releasedTo: number;
}
