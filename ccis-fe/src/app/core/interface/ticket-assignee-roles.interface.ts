export interface TicketAssigneeRoleConstant {
  requestor: string;
  requestorManager: string;
  receivingDepartment: string;
  ticketAssignee: string;
  topManApprover: string;
  vicePresident: string;
  president: string;
}

export const ITicketAssigneeRoleConstant: TicketAssigneeRoleConstant = {
  requestor: "REQUESTOR",
  requestorManager: "REQUESTOR_MANAGER",
  receivingDepartment: "RECEIVING_DEPARTMENT",
  ticketAssignee: "TICKET_ASSIGNEE",
  topManApprover: "TOP_MAN_APPROVER",
  vicePresident: "VICE_PRESIDENT",
  president: "PRESIDENT",
};
