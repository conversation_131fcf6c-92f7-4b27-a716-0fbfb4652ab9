import httpClient from "@clients/httpClient";
import { ICreatePadRequest } from "@interface/gam-request-pads";

const apiResource = "pad-request";

// Function to fetch Pad Request
export const getGamPadRequestService = async (
  params: {
    filter?: string;
    filterStatus?: string;
    page?: number;
    pageSize?: number;
    sort?: string;
    divisionFilter?: number;
    areaFilter?: number;
    formtypeFilter?: number;
    dateFrom?: string;
    dateTo?: string;
  } = {}
) => {
  const relations = "releasedTo|createdBy";

  // Perform a GET request to fetch Pad Request with filters
  let query = `${apiResource}?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}`;

  //Filter by division
  if (params?.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }

  //Filter by form type
  if (params?.formtypeFilter) {
    query += `&formTypeId[eq]=${params.formtypeFilter}`;
  }

  //Filter by area
  if (params?.areaFilter) {
    query += `&areaId[eq]=${params.areaFilter}`;
  }

  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(`${query}&${relations}&sort=id,asc`);
};

export const getRemainingPadsService = async (
  params: {
    filter?: string;
    filterStatus?: string;
    page?: number;
    pageSize?: number;
    rows?: number;
    sort?: string;
    divisionFilter?: number;
    areaFilter?: number;
    formtypeFilter?: number;
  } = {}
) => {
  const queryParams = new URLSearchParams();

  queryParams.append("page", (params.page ?? 1).toString());
  queryParams.append("pageSize", (params.rows ?? 10).toString());

  // Perform a GET request to fetch Remaining Pads
  let query = `${apiResource}/remaining-pads?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}`;

  return httpClient.get(`${query}`);
};

export const getLastSeriesNumberService = async (
  params: {
    filter?: string;
    filterStatus?: string;
    page?: number;
    pageSize?: number;
    rows?: number;
    sort?: string;
    divisionFilter?: number;
    areaFilter?: number;
    formtypeFilter?: number;
  } = {}
) => {
  const api = "pad-series";
  const relations = "cooperative|product|userIssuedBy|padAssignment";

  // Perform a GET request to fetch last pad series
  let query = `${api}?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}`;

  return httpClient.get(`${query}&${relations}&sort=id,asc`);
};

export const postGamPadRequestService = async (payload: ICreatePadRequest) => {
  return httpClient.post(`${apiResource}`, payload);
};
